
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

const perplexityApiKey = Deno.env.get('PERPLEXITY_API_KEY');
const supabaseUrl = Deno.env.get('SUPABASE_URL') ?? '';
const supabaseKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '';

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    if (!perplexityApiKey) {
      throw new Error("Perplexity API key not found");
    }

    const { query, carModel } = await req.json();

    if (!query) {
      throw new Error("No query provided");
    }

    const formattedQuery = carModel 
      ? `I have a ${carModel} with the following issue: ${query}. What might be wrong and how should I troubleshoot it?`
      : `My car has the following issue: ${query}. What might be wrong and how should I troubleshoot it?`;

    console.log(`Processing car diagnostic query: ${formattedQuery.slice(0, 100)}...`);

    const response = await fetch('https://api.perplexity.ai/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${perplexityApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'sonar-pro',
        messages: [
          {
            role: 'system',
            content: `You are an automotive diagnostic expert. Provide clear, concise troubleshooting advice for car problems. 
            Focus on the most likely causes based on the symptoms described and suggest practical next steps. 
            Include safety warnings when appropriate. Format your response with markdown headings and bullet points for readability.`
          },
          {
            role: 'user',
            content: formattedQuery
          }
        ],
        temperature: 0.2,
        max_tokens: 1000,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Perplexity API error:', response.status, errorText);
      throw new Error(`API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    
    // Log diagnostic results (partially)
    const result = data.choices[0].message.content;
    console.log(`Diagnostic result: ${result.slice(0, 100)}...`);

    // Create supabase client to log the query (optional)
    const supabase = createClient(supabaseUrl, supabaseKey);
    
    // Log this query to a diagnostics_log table if it exists
    try {
      await supabase.from('diagnostics_log').insert({
        query: query,
        car_model: carModel || null,
        result_preview: result.substring(0, 200)
      }).select();
    } catch (logError) {
      // Just log the error but don't fail the request
      console.error('Error logging diagnostic query:', logError);
    }

    return new Response(
      JSON.stringify({ 
        result: result
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  } catch (error) {
    console.error('Error in perplexity-diagnose function:', error);
    return new Response(
      JSON.stringify({ error: error.message }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
});
