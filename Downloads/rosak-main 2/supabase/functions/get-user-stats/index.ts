import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get total users
    const { data: usersData, error: usersError } = await supabaseClient.auth.admin.listUsers()
    if (usersError) throw usersError
    const totalUsers = usersData?.users?.length || 0

    // Get total searches
    const { count: searchesCount, error: searchesError } = await supabaseClient
      .from('searches')
      .select('*', { count: 'exact', head: true })
    
    if (searchesError) throw searchesError

    // Get searches in last 24 hours
    const oneDayAgo = new Date()
    oneDayAgo.setDate(oneDayAgo.getDate() - 1)
    
    const { count: recent24hCount, error: recent24hError } = await supabaseClient
      .from('searches')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', oneDayAgo.toISOString())
    
    if (recent24hError) throw recent24hError

    // Get searches per day (last 7 days)
    const sevenDaysAgo = new Date()
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
    
    const { data: searchesPerDayData, error: searchesPerDayError } = await supabaseClient
      .from('searches')
      .select('created_at')
      .gte('created_at', sevenDaysAgo.toISOString())
    
    if (searchesPerDayError) throw searchesPerDayError
    
    // Process searches per day
    const searchesByDay: Record<string, number> = {}
    searchesPerDayData.forEach(search => {
      const date = new Date(search.created_at).toLocaleDateString()
      searchesByDay[date] = (searchesByDay[date] || 0) + 1
    })
    
    const searchesPerDay = Object.entries(searchesByDay)
      .map(([date, count]) => ({ date, count }))
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

    // Get top search terms
    const { data: searchTermsData, error: searchTermsError } = await supabaseClient
      .from('searches')
      .select('query')
    
    if (searchTermsError) throw searchTermsError
    
    // Process top search terms
    const termCount: Record<string, number> = {}
    searchTermsData.forEach(search => {
      termCount[search.query] = (termCount[search.query] || 0) + 1
    })
    
    const topSearchTerms = Object.entries(termCount)
      .map(([term, count]) => ({ term, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)

    // Get top car models
    const { data: carModelsData, error: carModelsError } = await supabaseClient
      .from('searches')
      .select('car_model')
      .not('car_model', 'is', null)
    
    if (carModelsError) throw carModelsError
    
    // Process top car models
    const modelCount: Record<string, number> = {}
    carModelsData.forEach(search => {
      if (search.car_model) {
        modelCount[search.car_model] = (modelCount[search.car_model] || 0) + 1
      }
    })
    
    const topCarModels = Object.entries(modelCount)
      .map(([model, count]) => ({ model, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5)

    return new Response(
      JSON.stringify({
        totalUsers,
        totalSearches: searchesCount || 0,
        searchesLast24h: recent24hCount || 0,
        searchesPerDay,
        topSearchTerms,
        topCarModels
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      },
    )
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      },
    )
  }
}) 
