import React from 'react';
import { CommonFix } from '@/utils/commonFixes';

interface CommonFixesPartsProps {
  fix: CommonFix | null;
}

const CommonFixesParts: React.FC<CommonFixesPartsProps> = ({ fix }) => {
  if (!fix) {
    return null;
  }

  return (
    <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
      <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg">
        <h3 className="text-md font-medium text-blue-800 dark:text-blue-300 mb-2">
          Common Fixes
        </h3>
        <ul className="space-y-1">
          {fix.fixes.map((item, index) => (
            <li key={index} className="text-sm flex items-center text-gray-900 dark:text-gray-100">
              <span className="mr-2 text-blue-500 dark:text-blue-400">•</span>
              {item}
            </li>
          ))}
        </ul>
      </div>
      
      <div className="bg-purple-50 dark:bg-purple-900/30 p-4 rounded-lg">
        <h3 className="text-md font-medium text-purple-800 dark:text-purple-300 mb-2">
          Common Parts
        </h3>
        <ul className="space-y-1">
          {fix.parts.map((item, index) => (
            <li key={index} className="text-sm flex items-center text-gray-900 dark:text-gray-100">
              <span className="mr-2 text-purple-500 dark:text-purple-400">•</span>
              {item}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default CommonFixesParts;
