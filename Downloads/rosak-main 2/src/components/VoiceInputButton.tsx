
import React from 'react';
import { Mic, MicOff } from 'lucide-react';
import { startVoiceInput } from '@/utils/voiceInput';
import { toast } from '@/components/ui/use-toast';

interface VoiceInputButtonProps {
  isActive: boolean;
  setIsActive: (active: boolean) => void;
  onTranscriptReceived: (transcript: string) => void;
}

const VoiceInputButton = ({ 
  isActive, 
  setIsActive, 
  onTranscriptReceived 
}: VoiceInputButtonProps) => {
  
  const handleVoiceInput = () => {
    try {
      // Don't start voice again if already active
      if (isActive) return;
      
      // Initialize voice input
      const stopListening = startVoiceInput(
        (transcript) => {
          onTranscriptReceived(transcript);
          toast({
            title: "Voice input captured",
            description: transcript,
          });
        },
        setIsActive
      );
      
      // Set active state
      setIsActive(true);
      
      // Stop listening after 10 seconds if user doesn't speak
      const timeout = setTimeout(() => {
        if (stopListening) stopListening();
        setIsActive(false);
      }, 10000);
      
      return () => {
        clearTimeout(timeout);
        if (stopListening) stopListening();
      };
    } catch (error) {
      console.error('Error with voice input:', error);
      toast({
        variant: "destructive",
        title: "Voice input error",
        description: "Could not access microphone. Please check permissions.",
      });
      setIsActive(false);
    }
  };

  return (
    <button
      className={`p-2 rounded-full transition-colors ${isActive 
        ? 'bg-red-500 text-white hover:bg-red-600' 
        : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'}`}
      onClick={handleVoiceInput}
      title="Voice input"
      aria-label="Use voice input"
    >
      {isActive ? <MicOff size={20} /> : <Mic size={20} />}
    </button>
  );
};

export default VoiceInputButton;
