import React from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTrigger } from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import QuickLinks from './QuickLinks';
import FavoriteCarModels from './FavoriteCarModels';
import { Menu } from 'lucide-react';

interface UserDashboardProps {
  onSelectSearch: (query: string, carModel?: string) => void;
  onSelectCarModel: (carModel: string) => void;
}

const UserDashboard = ({ onSelectSearch, onSelectCarModel }: UserDashboardProps) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button 
          variant="outline"
          size="icon"
          className="h-9 w-9 p-0 border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700"
        >
          <Menu className="h-5 w-5 text-gray-700 dark:text-gray-200 filter dark:filter-none invert dark:invert-0" />
          <span className="sr-only">Open menu</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px] dark:bg-gray-800 dark:text-gray-200">
        <DialogHeader>
          <div className="flex justify-center py-4">
            <img src="/logo.png" alt="Logo" className="h-12 w-auto filter dark:filter-none invert dark:invert-0" />
          </div>
        </DialogHeader>
        
        <div className="mt-4 flex flex-col items-center">
          <QuickLinks />
          <FavoriteCarModels onSelectCarModel={onSelectCarModel} />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default UserDashboard;
