import React from 'react';
import AdvancedMode from './AdvancedMode';

interface AdvancedOptionsProps {
  isAdvanced: boolean;
  toggleAdvanced: () => void;
  carModel: string;
  onCarModelChange: (model: string) => void;
  onCarModelKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => void;
}

const AdvancedOptions = ({ 
  isAdvanced, 
  toggleAdvanced, 
  carModel, 
  onCarModelChange,
  onCarModelKeyPress
}: AdvancedOptionsProps) => {
  return (
    <div className="mt-1 flex flex-col items-center">
      {isAdvanced && (
        <div className="flex flex-col items-center w-full max-w-md">
          <AdvancedMode
            carModel={carModel}
            setCarModel={onCarModelChange}
            onKeyPress={onCarModelKeyPress}
          />
          <span
            className="advanced-toggle mt-2 text-purple-600 dark:text-lime-500 hover:text-purple-700 dark:hover:text-lime-600 cursor-pointer transition-colors"
            onClick={toggleAdvanced}
          >
            Hide Advanced Options
          </span>
        </div>
      )}
      {!isAdvanced && (
        <span
          className="advanced-toggle text-purple-600 dark:text-lime-500 hover:text-purple-700 dark:hover:text-lime-600 cursor-pointer transition-colors"
          onClick={toggleAdvanced}
        >
          Advanced Options
        </span>
      )}
    </div>
  );
};

export default AdvancedOptions;
