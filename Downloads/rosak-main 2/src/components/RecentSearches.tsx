
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { useUserPreferences } from '@/context/UserPreferencesContext';
import { Button } from '@/components/ui/button';
import { formatDistanceToNow } from 'date-fns';

interface RecentSearchesProps {
  onSelectSearch: (query: string, carModel?: string) => void;
}

const RecentSearches = ({ onSelectSearch }: RecentSearchesProps) => {
  const { recentSearches, clearRecentSearches } = useUserPreferences();
  
  if (recentSearches.length === 0) return null;
  
  return (
    <Card className="mb-6 dark:bg-gray-800 dark:border-gray-700">
      <CardHeader className="pb-2 flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-medium dark:text-gray-200">Recent Searches</CardTitle>
        {recentSearches.length > 0 && (
          <Button 
            variant="outline" 
            size="sm" 
            onClick={clearRecentSearches}
            className="text-xs dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
          >
            Clear
          </Button>
        )}
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex flex-col divide-y dark:divide-gray-700">
          {recentSearches.map((search, index) => (
            <div 
              key={index}
              className="py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 px-2 rounded"
              onClick={() => onSelectSearch(search.query, search.carModel)}
            >
              <p className="text-sm font-medium dark:text-gray-200">
                {search.query}
                {search.carModel && <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">{search.carModel}</span>}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {formatDistanceToNow(search.timestamp, { addSuffix: true })}
              </p>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default RecentSearches;
