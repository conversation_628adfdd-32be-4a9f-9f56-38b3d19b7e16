import React, { useState, useRef } from 'react';
import { Search } from 'lucide-react';
import VoiceInputButton from './VoiceInputButton';
import SuggestionDropdown from './SuggestionDropdown';
import { Suggestion } from '@/utils/autoSuggestions';

interface SearchInputProps {
  query: string;
  setQuery: (query: string) => void;
  suggestions: Suggestion[];
  showSuggestions: boolean;
  setShowSuggestions: (show: boolean) => void;
  onSearch: () => void;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onSuggestionSelect: (suggestion: string) => void;
  isSearching: boolean;
}

const SearchInput = ({ 
  query, 
  setQuery, 
  suggestions, 
  showSuggestions, 
  setShowSuggestions,
  onSearch,
  onInputChange,
  onKeyPress,
  onSuggestionSelect,
  isSearching
}: SearchInputProps) => {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [isVoiceActive, setIsVoiceActive] = useState(false);

  const handleVoiceTranscript = (transcript: string) => {
    setQuery(transcript);
  };

  return (
    <div className="w-full max-w-2xl">
      <div className="flex items-center relative">
        <input
          type="text"
          placeholder="Describe your car issue..."
          className="search-input pr-20 w-full"
          value={query}
          onChange={onInputChange}
          onKeyPress={onKeyPress}
          ref={searchInputRef}
          onFocus={() => query.length > 2 && setShowSuggestions(true)}
          onBlur={() => setTimeout(() => setShowSuggestions(false), 100)}
        />
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
          <VoiceInputButton
            isActive={isVoiceActive}
            setIsActive={setIsVoiceActive}
            onTranscriptReceived={handleVoiceTranscript}
          />
          <button
            className="search-button"
            onClick={onSearch}
            disabled={isSearching}
          >
            {isSearching ? "Diagnosing..." : <Search size={20} />}
          </button>
        </div>
      </div>

      {showSuggestions && (
        <div className="mt-2">
          <SuggestionDropdown
            suggestions={suggestions}
            onSelect={onSuggestionSelect}
          />
        </div>
      )}
    </div>
  );
};

export default SearchInput;
