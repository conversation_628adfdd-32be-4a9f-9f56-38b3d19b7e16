
import React from 'react';

interface SeverityIndicatorProps {
  severity: 'minor' | 'medium' | 'high' | 'critical' | null;
}

const SeverityIndicator: React.FC<SeverityIndicatorProps> = ({ severity }) => {
  if (!severity) {
    return null;
  }
  
  const getSeverityInfo = () => {
    switch (severity) {
      case 'minor':
        return {
          color: 'bg-green-100 border-green-200',
          textColor: 'text-green-800',
          label: 'Minor Issue',
          description: 'This is a minor issue that does not require immediate attention.'
        };
      case 'medium':
        return {
          color: 'bg-yellow-100 border-yellow-200',
          textColor: 'text-yellow-800',
          label: 'Medium Severity',
          description: 'This issue should be addressed soon but is not an emergency.'
        };
      case 'high':
        return {
          color: 'bg-orange-100 border-orange-200',
          textColor: 'text-orange-800',
          label: 'High Severity',
          description: 'This issue requires prompt attention to prevent further damage.'
        };
      case 'critical':
        return {
          color: 'bg-red-100 border-red-200',
          textColor: 'text-red-800',
          label: 'Critical Issue',
          description: 'This is a safety-critical issue that needs immediate attention.'
        };
      default:
        return {
          color: 'bg-gray-100 border-gray-200',
          textColor: 'text-gray-800',
          label: 'Unknown Severity',
          description: 'The severity of this issue could not be determined.'
        };
    }
  };
  
  const info = getSeverityInfo();
  
  return (
    <div className={`${info.color} border ${info.textColor} rounded-md p-3 mt-4`}>
      <div className="flex items-center">
        <div className={`w-3 h-3 rounded-full ${info.textColor.replace('text', 'bg')} mr-2`}></div>
        <h3 className="font-medium">{info.label}</h3>
      </div>
      <p className="text-sm mt-1">{info.description}</p>
    </div>
  );
};

export default SeverityIndicator;
