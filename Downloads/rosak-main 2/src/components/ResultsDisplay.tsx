import React, { useState, useEffect } from 'react';
import SeverityIndicator from './SeverityIndicator';
import CommonFixesParts from './CommonFixesParts';
import { getRelevantFixes } from '@/utils/commonFixes';
import { toast } from "@/components/ui/use-toast";

interface ResultsDisplayProps {
  result: string | null;
  isLoading: boolean;
  error: string | null;
}

const ResultsDisplay: React.FC<ResultsDisplayProps> = ({ result, isLoading, error }) => {
  const [severity, setSeverity] = useState<'minor' | 'medium' | 'high' | 'critical' | null>(null);
  const [commonFix, setCommonFix] = useState(null);

  useEffect(() => {
    if (result) {
      // Improved severity determination based on more specific patterns and context
      const lowerResult = result.toLowerCase();
      
      // Critical issues - safety-related or severe mechanical problems
      if ((lowerResult.includes('immediate') && lowerResult.includes('stop')) || 
          (lowerResult.includes('critical') && (lowerResult.includes('safety') || lowerResult.includes('accident'))) ||
          lowerResult.includes('fire hazard') || 
          lowerResult.includes('brake failure') ||
          lowerResult.includes('may cause crash')) {
        setSeverity('critical');
      } 
      // High issues - important but not immediately dangerous
      else if ((lowerResult.includes('serious') && lowerResult.includes('damage')) || 
               (lowerResult.includes('high') && lowerResult.includes('risk')) ||
               (lowerResult.includes('major') && lowerResult.includes('repair')) ||
               lowerResult.includes('transmission failure') ||
               lowerResult.includes('engine damage')) {
        setSeverity('high');
      } 
      // Medium issues - require attention but not urgently
      else if (lowerResult.includes('should be fixed soon') || 
               lowerResult.includes('moderate concern') ||
               (lowerResult.includes('check') && lowerResult.includes('soon')) ||
               lowerResult.includes('reduced performance')) {
        setSeverity('medium');
      } 
      // Default to minor for everything else
      else {
        setSeverity('minor');
      }

      // Get common fixes
      setCommonFix(getRelevantFixes(result));
      
      // Show toast notification
      toast({
        title: "Diagnostic Complete",
        description: "AI diagnostic analysis has been completed.",
      });
    }
  }, [result]);

  if (!result && !isLoading && !error) {
    return null;
  }

  return (
    <div className="search-results">
      {isLoading && (
        <div className="space-y-3">
          <div className="loading-pulse h-6 w-3/4 rounded-md"></div>
          <div className="loading-pulse h-4 w-full rounded-md"></div>
          <div className="loading-pulse h-4 w-5/6 rounded-md"></div>
          <div className="loading-pulse h-4 w-full rounded-md"></div>
          <div className="space-y-2 mt-6">
            <div className="loading-pulse h-5 w-1/3 rounded-md"></div>
            <div className="loading-pulse h-4 w-full rounded-md"></div>
            <div className="loading-pulse h-4 w-full rounded-md"></div>
          </div>
        </div>
      )}

      {error && (
        <div className="text-red-500 p-4 bg-red-50 rounded-md border border-red-100">
          <h3 className="font-medium mb-1">Error</h3>
          <p>{error}</p>
        </div>
      )}

      {result && !isLoading && !error && (
        <div>
          <div className="prose prose-slate max-w-none">
            <h2 className="text-2xl font-bold mb-3 text-gray-900 dark:text-white">Diagnostic Results</h2>
            <SeverityIndicator severity={severity} />
            <div className="mt-3 space-y-6">
              <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                <div className="space-y-4 text-black" dangerouslySetInnerHTML={{ __html: convertMarkdownToHtml(result) }} />
              </div>
            </div>
            
            <CommonFixesParts fix={commonFix} />

            <div className="mt-[20px] pt-3 pb-6 mb-[30px] border-t border-gray-100 text-sm text-gray-500">
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Simple markdown to HTML converter
function convertMarkdownToHtml(markdown: string): string {
  // First, split the content into sections based on headers
  const sections = markdown.split(/(?=## )/);
  
  // Process each section
  const processedSections = sections.map(section => {
    // Remove the ## prefix from headers and convert to HTML
    let processed = section
      .replace(/^## (.*?)(?:\n|$)/, '<h2 class="text-xl font-semibold mb-4 pb-2 border-b border-gray-200/50 dark:border-gray-700/30">$1</h2>')
      .replace(/### (.*?)(?:\n|$)/g, '<h3 class="text-lg font-semibold mt-6 mb-3">$1</h3>')
      .replace(/# (.*?)(?:\n|$)/g, '<h1 class="text-2xl font-bold mt-8 mb-4">$1</h1>');

    // Handle bullet points with increased indentation
    processed = processed.replace(/- (.*?)(?:\n|$)/g, '<li class="ml-12 list-disc my-2">$1</li>');
    
    // Handle numbered lists with whitespace and numbers
    processed = processed.replace(/(\d+)\.\s+(.*?)(?:\n|$)/g, function(match, number, content) {
      return `<div class="ml-8 my-2"><span class="mr-2">${number}.</span>${content}</div>`;
    });
    
    // Add whitespace before any conclusion text
    processed = processed.replace(
      /(By following these steps.*?)(?:\n|$)/g,
      '<div class="mt-0">$1</div>'
    );
    
    // Wrap lists in ul tags
    processed = processed.replace(/(<li.*?>.*?<\/li>)(?!\s*<li)/gs, '<ul class="my-4 space-y-2">$1</ul>');
    processed = processed.replace(/<\/li>\n<li/g, '</li><li>');

    // Handle line breaks
    processed = processed.replace(/\n\n/g, '<br><br>');
    
    // Handle bold and italic
    processed = processed.replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>');
    processed = processed.replace(/\*(.*?)\*/g, '<em class="italic">$1</em>');

    // Handle links
    processed = processed.replace(/\[(.*?)\]\((.*?)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 underline">$1</a>');

    // Remove any remaining hashtags
    processed = processed.replace(/#/g, '');

    return processed;
  });

  return processedSections.join('');
}

export default ResultsDisplay;
