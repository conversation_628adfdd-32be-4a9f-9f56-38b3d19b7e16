import React from 'react';
import { Link } from 'react-router-dom';

const SearchHeader = () => {
  const handleLogoClick = () => {
    window.location.reload();
  };

  return (
    <div className="text-center mb-6">
      <div className="flex justify-center pb-2.5">
        <Link to="/" onClick={handleLogoClick}>
          <img 
            src="/logo.png" 
            alt="RosakMY Logo" 
            className="w-[500px] h-[100px] object-contain filter dark:filter-none invert dark:invert-0 hover:opacity-80 transition-opacity cursor-pointer" 
          />
        </Link>
      </div>
      <p className="text-lg text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
        Type your car problem. Get fast, accurate AI-powered diagnostics in seconds.
      </p>
    </div>
  );
};

export default SearchHeader;
