import React, { useState, useEffect } from 'react';
import { WifiOff } from 'lucide-react';

const OfflineIndicator = () => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  
  console.log('Initial online status:', navigator.onLine);

  useEffect(() => {
    const handleOnline = () => {
      console.log('Online event triggered');
      setIsOnline(true);
    };
    const handleOffline = () => {
      console.log('Offline event triggered');
      setIsOnline(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (isOnline) return null;

  return (
    <div className="fixed bottom-4 right-4 bg-yellow-600 text-white px-3 py-2 rounded-lg shadow-lg flex items-center space-x-2 z-50">
      <WifiOff size={16} />
      <span className="text-sm font-medium">You're offline. Some features may be limited.</span>
    </div>
  );
};

export default OfflineIndicator;
