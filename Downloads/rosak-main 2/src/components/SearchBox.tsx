import React, { useState, useEffect } from 'react';
import { getSuggestions, Suggestion } from '@/utils/autoSuggestions';
import SearchHeader from './SearchHeader';
import SearchInput from './SearchInput';
import AdvancedOptions from './AdvancedOptions';
import { useUserPreferences } from '@/context/UserPreferencesContext';

interface SearchBoxProps {
  onSearch: (query: string, carModel?: string) => void;
  isSearching: boolean;
  initialQuery?: string;
  initialCarModel?: string;
}

const SearchBox = ({ onSearch, isSearching, initialQuery = '', initialCarModel = '' }: SearchBoxProps) => {
  const [query, setQuery] = useState(initialQuery);
  const [carModel, setCarModel] = useState(initialCarModel);
  const [isAdvanced, setIsAdvanced] = useState(false);
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const { addFavoriteCarModel } = useUserPreferences();

  useEffect(() => {
    if (initialQuery) {
      setQuery(initialQuery);
    }
    if (initialCarModel) {
      setCarModel(initialCarModel);
    }
  }, [initialQuery, initialCarModel]);

  const handleInputChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const newQuery = e.target.value;
    setQuery(newQuery);
    
    if (newQuery.length > 2) {
      const fetchedSuggestions = await getSuggestions(newQuery);
      setSuggestions(fetchedSuggestions);
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleCarModelKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  const handleSuggestionClick = (symptom: string) => {
    setQuery(symptom);
    setSuggestions([]);
    setShowSuggestions(false);
  };

  const handleSearch = () => {
    if (carModel) {
      // Parse the car model string into make, model, and year
      const parts = carModel.split(' ');
      if (parts.length >= 3) {
        const year = parts[parts.length - 1];
        const make = parts[0];
        const model = parts.slice(1, -1).join(' ');
        
        // Save to favorites
        addFavoriteCarModel({ make, model, year });
      }
    }
    onSearch(query, carModel);
  };

  const handleAdvancedToggle = () => {
    setIsAdvanced(!isAdvanced);
  };

  const handleCarModelChange = (newCarModel: string) => {
    setCarModel(newCarModel);
  };

  return (
    <div className="search-container flex flex-col items-center justify-center min-h-[calc(100vh-400px)]">
      <div className="w-full max-w-2xl">
        <SearchHeader />

        <SearchInput
          query={query}
          setQuery={setQuery}
          suggestions={suggestions}
          showSuggestions={showSuggestions}
          setShowSuggestions={setShowSuggestions}
          onSearch={handleSearch}
          onInputChange={handleInputChange}
          onKeyPress={handleKeyPress}
          onSuggestionSelect={handleSuggestionClick}
          isSearching={isSearching}
        />

        <div className="mt-[20px]">
          <AdvancedOptions
            isAdvanced={isAdvanced}
            toggleAdvanced={handleAdvancedToggle}
            carModel={carModel}
            onCarModelChange={handleCarModelChange}
            onCarModelKeyPress={handleCarModelKeyPress}
          />
        </div>
      </div>
    </div>
  );
};

export default SearchBox;
