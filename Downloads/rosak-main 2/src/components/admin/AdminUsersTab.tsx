import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from "@/components/ui/use-toast";
import { 
  Table, TableBody, TableCaption, TableCell, 
  TableHead, TableHeader, TableRow 
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ShieldCheck, ShieldOff } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

interface User {
  id: string;
  email: string;
  created_at: string;
  last_sign_in_at: string | null;
  is_admin: boolean;
}

const AdminUsersTab = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState('');
  const [selectedEmail, setSelectedEmail] = useState('');
  const [adminAction, setAdminAction] = useState<'add' | 'remove'>('add');

  const fetchUsers = async () => {
    setLoading(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new Error('No session');

      const response = await fetch(
        'https://gpeiyejoagghucvqmmdl.supabase.co/functions/v1/get-users',
        {
          headers: {
            'Authorization': `Bearer ${session.access_token}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch users');
      }
      
      const users = await response.json();
      setUsers(users);
    } catch (error: any) {
      console.error('Error fetching users:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: error.message || "Failed to load users",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  const handleAdminRoleChange = async () => {
    try {
      if (adminAction === 'add') {
        // Add admin role
        const { error } = await supabase
          .from('user_roles')
          .insert({
            user_id: selectedUserId,
            role: 'admin'
          });
        
        if (error) throw error;
        
        toast({
          title: "Admin Role Added",
          description: `${selectedEmail} is now an admin`,
        });
      } else {
        // Remove admin role
        const { error } = await supabase
          .from('user_roles')
          .delete()
          .eq('user_id', selectedUserId)
          .eq('role', 'admin');
        
        if (error) throw error;
        
        toast({
          title: "Admin Role Removed",
          description: `${selectedEmail} is no longer an admin`,
        });
      }
      
      // Update local state
      setUsers(users.map(user => 
        user.id === selectedUserId 
          ? { ...user, is_admin: adminAction === 'add' }
          : user
      ));
      
      setOpenDialog(false);
    } catch (error) {
      console.error('Error changing admin role:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update admin role",
      });
    }
  };

  const openAdminDialog = (userId: string, email: string, action: 'add' | 'remove') => {
    setSelectedUserId(userId);
    setSelectedEmail(email);
    setAdminAction(action);
    setOpenDialog(true);
  };

  if (loading) {
    return <div className="text-center py-8">Loading users...</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">All Users</h2>
        <Button onClick={fetchUsers} variant="outline" size="sm">
          Refresh
        </Button>
      </div>
      
      <Table>
        <TableCaption>List of all users</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>Email</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead>Last Login</TableHead>
            <TableHead>Role</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.length > 0 ? (
            users.map((user) => (
              <TableRow key={user.id}>
                <TableCell>{user.email}</TableCell>
                <TableCell>{new Date(user.created_at).toLocaleString()}</TableCell>
                <TableCell>
                  {user.last_sign_in_at 
                    ? new Date(user.last_sign_in_at).toLocaleString() 
                    : 'Never'
                  }
                </TableCell>
                <TableCell>
                  {user.is_admin ? (
                    <Badge variant="default" className="bg-green-600">Admin</Badge>
                  ) : (
                    <Badge variant="outline">User</Badge>
                  )}
                </TableCell>
                <TableCell className="text-right">
                  {user.is_admin ? (
                    <Button
                      onClick={() => openAdminDialog(user.id, user.email, 'remove')}
                      size="sm"
                      variant="outline"
                    >
                      <ShieldOff className="mr-1 h-4 w-4" />
                      Remove Admin
                    </Button>
                  ) : (
                    <Button
                      onClick={() => openAdminDialog(user.id, user.email, 'add')}
                      size="sm"
                      variant="outline"
                    >
                      <ShieldCheck className="mr-1 h-4 w-4" />
                      Make Admin
                    </Button>
                  )}
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={5} className="text-center py-8">
                No users found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>

      <Dialog open={openDialog} onOpenChange={setOpenDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {adminAction === 'add' 
                ? 'Add Admin Role' 
                : 'Remove Admin Role'}
            </DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <p>
              {adminAction === 'add'
                ? `Are you sure you want to make ${selectedEmail} an admin? They will have full access to all data.`
                : `Are you sure you want to remove admin role from ${selectedEmail}?`}
            </p>
          </div>
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setOpenDialog(false)}>
              Cancel
            </Button>
            <Button 
              onClick={handleAdminRoleChange}
              variant={adminAction === 'add' ? 'default' : 'destructive'}
            >
              {adminAction === 'add' ? 'Add Admin Role' : 'Remove Admin Role'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AdminUsersTab;
