import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from "@/components/ui/use-toast";
import { 
  Table, TableBody, TableCaption, TableCell, 
  TableHead, TableHeader, TableRow 
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Trash2, Edit, Save, X } from 'lucide-react';
import { Input } from '@/components/ui/input';

interface Search {
  id: string;
  query: string;
  car_model: string | null;
  created_at: string;
  user_email?: string;
}

const AdminSearchesTab = () => {
  const [searches, setSearches] = useState<Search[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [editForm, setEditForm] = useState<{ query: string, car_model: string | null }>({
    query: '',
    car_model: null
  });

  const fetchSearches = async () => {
    setLoading(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) throw new Error('No session');

      // Get all searches
      const { data, error } = await supabase
        .from('searches')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;

      // Format the searches with email information
      const formattedSearches = await Promise.all(data.map(async (search) => {
        let userEmail = 'Unknown';
        
        // If there's a user_id, try to get their email from the Edge Function
        if (search.user_id) {
          const response = await fetch(
            'https://gpeiyejoagghucvqmmdl.supabase.co/functions/v1/get-user-email',
            {
              method: 'POST',
              headers: {
                'Authorization': `Bearer ${session.access_token}`,
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({ userId: search.user_id }),
            }
          );

          if (response.ok) {
            const { email } = await response.json();
            userEmail = email;
          }
        }
        
        return {
          id: search.id,
          query: search.query,
          car_model: search.car_model,
          created_at: search.created_at,
          user_email: userEmail
        };
      }));
      
      setSearches(formattedSearches);
    } catch (error) {
      console.error('Error fetching searches:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load searches",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSearches();
  }, []);

  const handleDelete = async (id: string) => {
    try {
      const { error } = await supabase
        .from('searches')
        .delete()
        .eq('id', id);
      
      if (error) throw error;
      
      setSearches(searches.filter(search => search.id !== id));
      toast({
        title: "Search Deleted",
        description: "The search has been successfully deleted",
      });
    } catch (error) {
      console.error('Error deleting search:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete search",
      });
    }
  };

  const startEdit = (search: Search) => {
    setEditingId(search.id);
    setEditForm({
      query: search.query,
      car_model: search.car_model
    });
  };

  const cancelEdit = () => {
    setEditingId(null);
  };

  const handleEditChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setEditForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const saveEdit = async () => {
    if (!editingId) return;
    
    try {
      const { error } = await supabase
        .from('searches')
        .update({
          query: editForm.query,
          car_model: editForm.car_model
        })
        .eq('id', editingId);
      
      if (error) throw error;
      
      // Update local state
      setSearches(searches.map(search => 
        search.id === editingId 
          ? { ...search, query: editForm.query, car_model: editForm.car_model }
          : search
      ));
      
      toast({
        title: "Search Updated",
        description: "The search has been successfully updated",
      });
      setEditingId(null);
    } catch (error) {
      console.error('Error updating search:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update search",
      });
    }
  };

  if (loading) {
    return <div className="text-center py-8">Loading searches...</div>;
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">All Searches</h2>
        <Button onClick={fetchSearches} variant="outline" size="sm">
          Refresh
        </Button>
      </div>
      
      <Table>
        <TableCaption>List of all user searches</TableCaption>
        <TableHeader>
          <TableRow>
            <TableHead>Query</TableHead>
            <TableHead>Car Model</TableHead>
            <TableHead>User</TableHead>
            <TableHead>Date</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {searches.length > 0 ? (
            searches.map((search) => (
              <TableRow key={search.id}>
                <TableCell>
                  {editingId === search.id ? (
                    <Input
                      name="query"
                      value={editForm.query}
                      onChange={handleEditChange}
                      className="w-full"
                    />
                  ) : (
                    search.query
                  )}
                </TableCell>
                <TableCell>
                  {editingId === search.id ? (
                    <Input
                      name="car_model"
                      value={editForm.car_model || ''}
                      onChange={handleEditChange}
                      className="w-full"
                    />
                  ) : (
                    search.car_model || 'N/A'
                  )}
                </TableCell>
                <TableCell>{search.user_email}</TableCell>
                <TableCell>{new Date(search.created_at).toLocaleString()}</TableCell>
                <TableCell className="text-right">
                  {editingId === search.id ? (
                    <div className="flex justify-end space-x-2">
                      <Button onClick={saveEdit} size="sm" variant="outline">
                        <Save className="h-4 w-4" />
                      </Button>
                      <Button onClick={cancelEdit} size="sm" variant="outline">
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ) : (
                    <div className="flex justify-end space-x-2">
                      <Button onClick={() => startEdit(search)} size="sm" variant="outline">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button 
                        onClick={() => handleDelete(search.id)} 
                        size="sm" 
                        variant="destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </TableCell>
              </TableRow>
            ))
          ) : (
            <TableRow>
              <TableCell colSpan={5} className="text-center py-8">
                No searches found
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  );
};

export default AdminSearchesTab;
