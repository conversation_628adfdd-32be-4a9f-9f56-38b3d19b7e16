
import React, { useState } from 'react';
import { FollowUpQuestion, QuestionCategory, followUpQuestions } from '@/utils/followUpQuestions';

interface FollowUpQuestionsProps {
  category: string | null;
  onAnswer: (question: string, answer: string) => void;
}

const FollowUpQuestions: React.FC<FollowUpQuestionsProps> = ({ 
  category,
  onAnswer
}) => {
  const [currentQuestionId, setCurrentQuestionId] = useState<string>("start");
  const [answered, setAnswered] = useState<boolean>(false);
  
  if (!category || !followUpQuestions[category] || answered) {
    return null;
  }
  
  const questionCategory: QuestionCategory = followUpQuestions[category];
  const currentQuestion: FollowUpQuestion = questionCategory.questionTree[currentQuestionId];
  
  if (!currentQuestion) {
    return null;
  }

  const handleResponseClick = (responseText: string, nextQuestionId?: string) => {
    onAnswer(currentQuestion.question, responseText);
    
    if (nextQuestionId) {
      setCurrentQuestionId(nextQuestionId);
    } else {
      setAnswered(true);
    }
  };

  return (
    <div className="mt-4 p-4 bg-gray-50 border border-gray-100 rounded-lg">
      <h3 className="text-md font-medium text-gray-800 mb-2">
        {questionCategory.category}: {currentQuestion.question}
      </h3>
      <div className="flex flex-wrap gap-2 mt-3">
        {currentQuestion.responses.map((response, index) => (
          <button
            key={index}
            className="px-3 py-1.5 text-sm bg-white border border-gray-200 rounded-full hover:bg-gray-50 transition-colors"
            onClick={() => handleResponseClick(response.text, response.nextQuestionId)}
          >
            {response.text}
          </button>
        ))}
      </div>
    </div>
  );
};

export default FollowUpQuestions;
