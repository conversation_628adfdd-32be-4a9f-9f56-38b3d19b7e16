import React from 'react';
import { Suggestion } from '@/utils/autoSuggestions';

interface SuggestionDropdownProps {
  suggestions: Suggestion[];
  onSelect: (suggestion: string) => void;
}

const SuggestionDropdown: React.FC<SuggestionDropdownProps> = ({ 
  suggestions,
  onSelect
}) => {
  if (suggestions.length === 0) {
    return null;
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'minor': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-[300px] overflow-y-auto">
      <div className="p-2 border-b border-gray-100 dark:border-gray-700">
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">Common Problems</h3>
      </div>
      <ul className="py-1">
        {suggestions.map((suggestion, index) => (
          <li
            key={index}
            className="px-4 py-2 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center justify-between"
            onClick={() => onSelect(suggestion.symptom)}
          >
            <span className="text-sm text-gray-700 dark:text-gray-300">{suggestion.symptom}</span>
            <span className={`text-xs font-medium px-2 py-1 rounded ${getSeverityColor(suggestion.severity)}`}>
              {suggestion.severity}
            </span>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default SuggestionDropdown;
