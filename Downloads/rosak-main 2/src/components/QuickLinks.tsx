import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { useUserPreferences } from '@/context/UserPreferencesContext';
import { Icon } from '@/components/ui/icon';
import { Button } from '@/components/ui/button';

const QuickLinks = () => {
  const { quickLinks } = useUserPreferences();
  
  return (
    <Card className="mb-6 dark:bg-gray-800 dark:border-gray-700 w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium dark:text-gray-200 text-center">Quick Links</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex flex-wrap gap-2 justify-center">
          {quickLinks.map((link, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              asChild
              className="dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
            >
              <a href={link.url} target="_blank" rel="noopener noreferrer">
                {/* Use Icon component with dynamic icons */}
                <span className="flex items-center gap-2">
                  {link.icon === 'phone-call' && <span>☎️</span>}
                  {link.icon === 'truck' && <span>🚚</span>}
                  {link.icon === 'map-pin' && <span>📍</span>}
                  {link.name}
                </span>
              </a>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default QuickLinks;
