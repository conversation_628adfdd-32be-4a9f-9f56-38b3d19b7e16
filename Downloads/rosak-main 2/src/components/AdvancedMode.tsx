import React from 'react';

interface AdvancedModeProps {
  carModel: string;
  setCarModel: (model: string) => void;
  onKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => void;
}

const AdvancedMode: React.FC<AdvancedModeProps> = ({ carModel, setCarModel, onKeyPress }) => {
  return (
    <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
      <h3 className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">Advanced Mode</h3>
      <div className="flex flex-col space-y-2">
        <label className="text-sm text-gray-600 dark:text-gray-300">Car Model (for more accurate diagnosis)</label>
        <input
          type="text"
          value={carModel}
          onChange={(e) => setCarModel(e.target.value)}
          onKeyPress={onKeyPress}
          placeholder="e.g. Toyota Camry 2020"
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400"
        />
        <p className="text-xs text-gray-500 dark:text-gray-400">
          Adding your car model helps our AI provide more specific troubleshooting advice.
        </p>
      </div>
    </div>
  );
};

export default AdvancedMode;
