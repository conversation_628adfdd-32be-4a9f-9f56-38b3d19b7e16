import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { useUserPreferences } from '@/context/UserPreferencesContext';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

interface FavoriteCarModelsProps {
  onSelectCarModel: (carModel: string) => void;
}

const FavoriteCarModels = ({ onSelectCarModel }: FavoriteCarModelsProps) => {
  const { favoriteCarModels, removeFavoriteCarModel } = useUserPreferences();
  
  return (
    <Card className="mb-6 dark:bg-gray-800 dark:border-gray-700">
      <CardHeader className="pb-2">
        <CardTitle className="text-lg font-medium dark:text-gray-200">Favorite Cars</CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        {favoriteCarModels.length === 0 ? (
          <p className="text-sm text-gray-500 dark:text-gray-400">
            No favorite car models yet. Add a car model in advanced mode to save it here.
          </p>
        ) : (
          <div className="flex flex-wrap gap-2">
            {favoriteCarModels.map((carModel, index) => {
              const modelString = `${carModel.year} ${carModel.make} ${carModel.model}`;
              
              return (
                <div key={index} className="flex items-center">
                  <Button
                    variant="outline"
                    size="sm"
                    className="mr-1 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
                    onClick={() => onSelectCarModel(modelString)}
                  >
                    {modelString}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="p-1 h-6 w-6 dark:text-gray-400 dark:hover:text-gray-200"
                    onClick={() => removeFavoriteCarModel(index)}
                  >
                    <X size={12} />
                  </Button>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default FavoriteCarModels;
