
import React from 'react';
import { icons } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface IconProps extends React.HTMLAttributes<HTMLSpanElement> {
  name?: keyof typeof icons;
  color?: string;
  size?: number;
}

export const Icon = ({ name, color, size = 24, className, ...props }: IconProps) => {
  if (!name) return null;
  
  const LucideIcon = icons[name];
  
  if (!LucideIcon) {
    console.warn(`Icon not found: ${name}`);
    return null;
  }
  
  return (
    <span className={cn('inline-flex', className)} {...props}>
      <LucideIcon color={color} size={size} />
    </span>
  );
};
