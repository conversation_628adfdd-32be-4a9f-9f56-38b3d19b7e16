import React from 'react';
import { Copyright } from 'lucide-react';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  
  return (
    <footer className="border-t border-gray-100 dark:border-gray-800 mt-auto py-4 px-4">
      <div className="container mx-auto">
        <div className="flex items-center justify-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            © {currentYear} Rosak.MY - Powered by <a href="https://www.perplexity.ai" target="_blank" rel="noopener noreferrer" className="text-purple-600 dark:text-lime-400 hover:text-purple-800 dark:hover:text-lime-300 underline">Perplexity AI</a>. All rights reserved. (Results are for informational purposes only)
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
