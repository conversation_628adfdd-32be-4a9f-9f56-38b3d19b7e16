
// Perplexity API integration for RosakMY vehicle diagnostics

import { getSuggestions, Suggestion } from './autoSuggestions';
import { supabase } from "@/integrations/supabase/client";

export async function fetchDiagnostic(query: string, carModel?: string): Promise<string> {
  try {
    // Call our Supabase Edge Function which securely uses the Perplexity API
    const { data, error } = await supabase.functions.invoke("perplexity-diagnose", {
      body: { query, carModel },
    });

    if (error) {
      console.error("Edge function error:", error);
      throw new Error(error.message || "Failed to generate diagnostic");
    }

    if (!data?.result) {
      throw new Error("No diagnostic result returned");
    }

    return data.result;
  } catch (error) {
    console.error("API Error:", error);
    
    // As a fallback, use the placeholder implementation
    return fallbackDiagnostic(query, carModel);
  }
}

// Fallback implementation for development or when API is unavailable
function fallbackDiagnostic(query: string, carModel?: string): Promise<string> {
  return new Promise((resolve) => {
    // Simulate API call delay
    setTimeout(() => {
      // Example response format
      const formattedQuery = carModel 
        ? `Problem: ${query}\nCar Model: ${carModel}`
        : `Problem: ${query}`;
        
      const response = `## Diagnostic Results
      
Based on your description: "${formattedQuery}"
      
### Possible Issues:
1. The symptoms you're describing suggest it could be related to [problem area].
2. This is commonly seen in [relevant systems] and might indicate [potential issue].

### Recommended Actions:
- First, check [specific component] for signs of [specific issue].
- Consider having a professional inspect your [system name].
- If you notice [additional symptom], this would confirm the diagnosis.

### Safety Notice:
This is an AI-generated diagnostic suggestion. For safety-critical issues, always consult with a qualified mechanic.`;
        
      resolve(response);
    }, 2000); // Simulate 2-second API delay
  });
}
