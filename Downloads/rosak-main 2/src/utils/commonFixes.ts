
export interface CommonFix {
  problem: string;
  fixes: string[];
  parts: string[];
}

export const commonFixes: CommonFix[] = [
  {
    problem: "brake",
    fixes: [
      "Replace brake pads",
      "Resurface or replace rotors",
      "Check brake fluid level",
      "Bleed brake lines"
    ],
    parts: [
      "Brake pads",
      "Brake rotors",
      "Brake fluid",
      "Brake caliper"
    ]
  },
  {
    problem: "engine",
    fixes: [
      "Replace spark plugs",
      "Clean throttle body",
      "Check engine mounts",
      "Replace air filter"
    ],
    parts: [
      "Spark plugs",
      "Air filter",
      "Fuel filter",
      "Ignition coil"
    ]
  },
  {
    problem: "battery",
    fixes: [
      "Replace battery",
      "Clean battery terminals",
      "Check alternator",
      "Test electrical system"
    ],
    parts: [
      "Car battery",
      "Battery terminals",
      "Alternator",
      "Battery cables"
    ]
  },
  {
    problem: "transmission",
    fixes: [
      "Check transmission fluid",
      "Replace transmission filter",
      "Adjust shift linkage",
      "Inspect for leaks"
    ],
    parts: [
      "Transmission fluid",
      "Transmission filter",
      "Solenoid pack",
      "Clutch kit"
    ]
  },
  {
    problem: "tire",
    fixes: [
      "Rotate tires",
      "Balance wheels",
      "Check alignment",
      "Inspect for punctures"
    ],
    parts: [
      "Tires",
      "Wheel weights",
      "Valve stems",
      "TPMS sensors"
    ]
  }
];

export const getRelevantFixes = (query: string): CommonFix | null => {
  const lowerQuery = query.toLowerCase();
  
  for (const fix of commonFixes) {
    if (lowerQuery.includes(fix.problem)) {
      return fix;
    }
  }
  
  return null;
};
