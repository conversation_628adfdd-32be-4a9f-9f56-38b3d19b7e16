
export const startVoiceInput = (
  setInputValue: (value: string) => void,
  setIsListening: (value: boolean) => void
): (() => void) => {
  const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
  
  if (!SpeechRecognition) {
    console.error("Speech recognition not supported in this browser");
    return () => {};
  }

  const recognition = new SpeechRecognition();
  recognition.continuous = false;
  recognition.lang = 'en-US';
  recognition.interimResults = false;
  recognition.maxAlternatives = 1;

  recognition.onstart = () => {
    setIsListening(true);
    console.log('Voice recognition started');
  };

  recognition.onresult = (event) => {
    const transcript = event.results[0][0].transcript;
    console.log('Voice input received:', transcript);
    setInputValue(transcript);
  };

  recognition.onerror = (event) => {
    console.error('Speech recognition error', event.error);
    setIsListening(false);
  };

  recognition.onend = () => {
    console.log('Voice recognition ended');
    setIsListening(false);
  };

  // Start the recognition
  try {
    recognition.start();
    console.log('Attempting to start voice recognition');
  } catch (error) {
    console.error('Failed to start voice recognition:', error);
    setIsListening(false);
  }

  // Return a function to stop listening
  return () => {
    try {
      recognition.stop();
    } catch (error) {
      console.error('Error stopping voice recognition:', error);
    }
  };
};
