
export interface Suggestion {
  symptom: string;
  severity: 'minor' | 'medium' | 'high' | 'critical';
}

export const commonSymptoms: Suggestion[] = [
  { symptom: "Engine shaking when idle", severity: "medium" },
  { symptom: "Brakes squealing", severity: "high" },
  { symptom: "Check engine light on", severity: "medium" },
  { symptom: "Battery not holding charge", severity: "high" },
  { symptom: "Car won't start", severity: "critical" },
  { symptom: "Transmission slipping", severity: "high" },
  { symptom: "Power steering fluid leaking", severity: "medium" },
  { symptom: "AC not cooling", severity: "minor" },
  { symptom: "Unusual noises when turning", severity: "medium" },
  { symptom: "Oil leaking", severity: "high" },
  { symptom: "Tire pressure light on", severity: "minor" },
  { symptom: "Burning smell from engine", severity: "critical" },
  { symptom: "Steering wheel vibration", severity: "medium" },
  { symptom: "Windshield wipers not working", severity: "minor" },
  { symptom: "Headlights dim or flickering", severity: "medium" },
];

export const getSuggestions = (input: string): Suggestion[] => {
  if (!input || input.length < 3) return [];
  
  const lowerInput = input.toLowerCase();
  
  return commonSymptoms
    .filter(item => item.symptom.toLowerCase().includes(lowerInput))
    .slice(0, 5);
};
