
export interface FollowUpQuestion {
  id: string;
  question: string;
  responses: {
    text: string;
    nextQuestionId?: string;
  }[];
}

export interface QuestionCategory {
  category: string;
  questionTree: Record<string, FollowUpQuestion>;
}

export const followUpQuestions: Record<string, QuestionCategory> = {
  "engine": {
    category: "Engine Issues",
    questionTree: {
      "start": {
        id: "start",
        question: "When does the engine issue occur?",
        responses: [
          { text: "When starting the car", nextQuestionId: "starting" },
          { text: "While driving", nextQuestionId: "driving" },
          { text: "When idle", nextQuestionId: "idle" }
        ]
      },
      "starting": {
        id: "starting",
        question: "What happens when you try to start the car?",
        responses: [
          { text: "Nothing happens at all" },
          { text: "There's a clicking sound" },
          { text: "Engine cranks but doesn't start" }
        ]
      },
      "driving": {
        id: "driving",
        question: "What symptoms do you notice while driving?",
        responses: [
          { text: "Engine stalling" },
          { text: "Loss of power" },
          { text: "Unusual noises" }
        ]
      },
      "idle": {
        id: "idle",
        question: "How does the engine behave when idle?",
        responses: [
          { text: "Rough idling" },
          { text: "RPM fluctuating" },
          { text: "Engine shuts off" }
        ]
      }
    }
  },
  "brakes": {
    category: "Brake Issues",
    questionTree: {
      "start": {
        id: "start",
        question: "What brake symptoms are you experiencing?",
        responses: [
          { text: "Squealing or grinding noises", nextQuestionId: "noises" },
          { text: "Reduced braking performance", nextQuestionId: "performance" },
          { text: "Vibration when braking", nextQuestionId: "vibration" }
        ]
      },
      "noises": {
        id: "noises",
        question: "When do you hear these noises?",
        responses: [
          { text: "When applying brakes" },
          { text: "All the time while driving" },
          { text: "Only in the morning" }
        ]
      },
      "performance": {
        id: "performance",
        question: "How has the braking performance changed?",
        responses: [
          { text: "Need to press harder" },
          { text: "Brakes feel spongy" },
          { text: "Car pulls to one side when braking" }
        ]
      },
      "vibration": {
        id: "vibration",
        question: "Where do you feel the vibration when braking?",
        responses: [
          { text: "Steering wheel" },
          { text: "Brake pedal" },
          { text: "Throughout the car" }
        ]
      }
    }
  }
};

export const getRelevantQuestionCategory = (query: string): string | null => {
  const lowerQuery = query.toLowerCase();
  
  if (lowerQuery.includes("engine") || lowerQuery.includes("start") || 
      lowerQuery.includes("idle") || lowerQuery.includes("power")) {
    return "engine";
  }
  
  if (lowerQuery.includes("brake") || lowerQuery.includes("stop") || 
      lowerQuery.includes("pedal") || lowerQuery.includes("squealing")) {
    return "brakes";
  }
  
  return null;
};
