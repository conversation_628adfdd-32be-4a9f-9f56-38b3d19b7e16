
import React, { createContext, useContext, useEffect, useState } from 'react';

export interface CarModel {
  make: string;
  model: string;
  year: string;
}

export interface RecentSearch {
  query: string;
  carModel?: string;
  timestamp: number;
}

export interface QuickLink {
  name: string;
  url: string;
  icon: string;
}

interface UserPreferencesContextType {
  recentSearches: RecentSearch[];
  favoriteCarModels: CarModel[];
  quickLinks: QuickLink[];
  addRecentSearch: (search: Omit<RecentSearch, 'timestamp'>) => void;
  addFavoriteCarModel: (carModel: CarModel) => void;
  removeFavoriteCarModel: (index: number) => void;
  clearRecentSearches: () => void;
}

const defaultQuickLinks: QuickLink[] = [
  { name: 'Emergency Services', url: 'tel:911', icon: 'phone-call' },
  { name: 'Tow Services', url: 'https://www.aaa.com/roadside-assistance', icon: 'truck' },
  { name: 'Find Repair Shops', url: 'https://maps.google.com/?q=auto+repair+shops+near+me', icon: 'map-pin' }
];

const UserPreferencesContext = createContext<UserPreferencesContextType | undefined>(undefined);

export const UserPreferencesProvider = ({ children }: { children: React.ReactNode }) => {
  const [recentSearches, setRecentSearches] = useState<RecentSearch[]>(() => {
    const saved = localStorage.getItem('recentSearches');
    return saved ? JSON.parse(saved) : [];
  });
  
  const [favoriteCarModels, setFavoriteCarModels] = useState<CarModel[]>(() => {
    const saved = localStorage.getItem('favoriteCarModels');
    return saved ? JSON.parse(saved) : [];
  });
  
  const [quickLinks, setQuickLinks] = useState<QuickLink[]>(() => {
    const saved = localStorage.getItem('quickLinks');
    return saved ? JSON.parse(saved) : defaultQuickLinks;
  });

  useEffect(() => {
    localStorage.setItem('recentSearches', JSON.stringify(recentSearches));
  }, [recentSearches]);

  useEffect(() => {
    localStorage.setItem('favoriteCarModels', JSON.stringify(favoriteCarModels));
  }, [favoriteCarModels]);

  useEffect(() => {
    localStorage.setItem('quickLinks', JSON.stringify(quickLinks));
  }, [quickLinks]);

  const addRecentSearch = (search: Omit<RecentSearch, 'timestamp'>) => {
    const newSearch = { ...search, timestamp: Date.now() };
    setRecentSearches(prev => {
      // Remove duplicates and limit to 5 recent searches
      const filtered = prev.filter(item => !(item.query === search.query && item.carModel === search.carModel));
      return [newSearch, ...filtered].slice(0, 5);
    });
  };

  const addFavoriteCarModel = (carModel: CarModel) => {
    setFavoriteCarModels(prev => {
      // Check for duplicates
      const isDuplicate = prev.some(model => 
        model.make === carModel.make && 
        model.model === carModel.model && 
        model.year === carModel.year
      );
      if (isDuplicate) return prev;
      return [...prev, carModel];
    });
  };

  const removeFavoriteCarModel = (index: number) => {
    setFavoriteCarModels(prev => prev.filter((_, i) => i !== index));
  };

  const clearRecentSearches = () => setRecentSearches([]);

  return (
    <UserPreferencesContext.Provider
      value={{
        recentSearches,
        favoriteCarModels,
        quickLinks,
        addRecentSearch,
        addFavoriteCarModel,
        removeFavoriteCarModel,
        clearRecentSearches
      }}
    >
      {children}
    </UserPreferencesContext.Provider>
  );
};

export const useUserPreferences = () => {
  const context = useContext(UserPreferencesContext);
  if (context === undefined) {
    throw new Error('useUserPreferences must be used within a UserPreferencesProvider');
  }
  return context;
};
