// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://gpeiyejoagghucvqmmdl.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImdwZWl5ZWpvYWdnaHVjdnFtbWRsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc0NjcyMTQsImV4cCI6MjA2MzA0MzIxNH0.ysnqgFPhX5AbQKSYK6EJYs85LlA7LQg_rufxlWZSdq4";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);