import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { ArrowLeft, Search, Book, Wrench, AlertTriangle, Calendar, Clock } from 'lucide-react';
import Footer from '@/components/Footer';
import DarkModeToggle from '@/components/DarkModeToggle';

interface KnowledgeItem {
  id: string;
  title: string;
  excerpt: string;
  category: string;
  difficulty: 'Beginner' | 'Intermediate' | 'Advanced';
  readTime: number;
  tags: string[];
  icon: React.ReactNode;
}

const knowledgeItems: KnowledgeItem[] = [
  {
    id: '1',
    title: 'Complete Car Maintenance Schedule for Malaysia',
    excerpt: 'A comprehensive guide to maintaining your vehicle in Malaysian climate conditions.',
    category: 'Maintenance',
    difficulty: 'Beginner',
    readTime: 10,
    tags: ['maintenance', 'schedule', 'climate'],
    icon: <Calendar className="h-6 w-6" />
  },
  {
    id: '2',
    title: 'DIY Oil Change: Step-by-Step Guide',
    excerpt: 'Learn how to change your car\'s engine oil safely at home with proper tools and techniques.',
    category: 'DIY Repairs',
    difficulty: 'Intermediate',
    readTime: 15,
    tags: ['oil-change', 'diy', 'engine'],
    icon: <Wrench className="h-6 w-6" />
  },
  {
    id: '3',
    title: 'Understanding Engine Warning Signs',
    excerpt: 'Recognize early symptoms of engine problems before they become expensive repairs.',
    category: 'Troubleshooting',
    difficulty: 'Beginner',
    readTime: 8,
    tags: ['engine', 'warning-signs', 'diagnostics'],
    icon: <AlertTriangle className="h-6 w-6" />
  },
  {
    id: '4',
    title: 'Brake System Maintenance and Safety',
    excerpt: 'Essential brake maintenance tips and safety checks every Malaysian driver should know.',
    category: 'Safety',
    difficulty: 'Intermediate',
    readTime: 12,
    tags: ['brakes', 'safety', 'maintenance'],
    icon: <AlertTriangle className="h-6 w-6" />
  },
  {
    id: '5',
    title: 'Air Conditioning Service Guide',
    excerpt: 'Keep your AC running efficiently in Malaysia\'s hot climate with proper maintenance.',
    category: 'Maintenance',
    difficulty: 'Intermediate',
    readTime: 10,
    tags: ['air-conditioning', 'climate', 'service'],
    icon: <Wrench className="h-6 w-6" />
  },
  {
    id: '6',
    title: 'Tire Care and Replacement Guide',
    excerpt: 'Everything about tire maintenance, rotation, and knowing when to replace them.',
    category: 'Maintenance',
    difficulty: 'Beginner',
    readTime: 7,
    tags: ['tires', 'safety', 'replacement'],
    icon: <Book className="h-6 w-6" />
  },
  {
    id: '7',
    title: 'Battery Maintenance in Tropical Climate',
    excerpt: 'Extend your car battery life in Malaysia\'s hot and humid conditions.',
    category: 'Maintenance',
    difficulty: 'Beginner',
    readTime: 6,
    tags: ['battery', 'tropical', 'maintenance'],
    icon: <Book className="h-6 w-6" />
  },
  {
    id: '8',
    title: 'Transmission Fluid Check and Change',
    excerpt: 'Learn how to check and maintain your transmission fluid for smooth operation.',
    category: 'DIY Repairs',
    difficulty: 'Advanced',
    readTime: 20,
    tags: ['transmission', 'fluid', 'maintenance'],
    icon: <Wrench className="h-6 w-6" />
  },
  {
    id: '9',
    title: 'Emergency Roadside Troubleshooting',
    excerpt: 'Quick fixes and safety procedures when your car breaks down on Malaysian roads.',
    category: 'Emergency',
    difficulty: 'Intermediate',
    readTime: 12,
    tags: ['emergency', 'roadside', 'safety'],
    icon: <AlertTriangle className="h-6 w-6" />
  }
];

const categories = ['All', 'Maintenance', 'DIY Repairs', 'Troubleshooting', 'Safety', 'Emergency'];
const difficulties = ['All', 'Beginner', 'Intermediate', 'Advanced'];

const KnowledgeBase = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedDifficulty, setSelectedDifficulty] = useState('All');

  const filteredItems = knowledgeItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    const matchesCategory = selectedCategory === 'All' || item.category === selectedCategory;
    const matchesDifficulty = selectedDifficulty === 'All' || item.difficulty === selectedDifficulty;
    return matchesSearch && matchesCategory && matchesDifficulty;
  });

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'Advanced': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 flex flex-col">
      {/* Header */}
      <header className="border-b border-gray-100 dark:border-gray-800 py-4 px-4">
        <div className="container mx-auto flex items-center justify-between">
          <Link to="/">
            <Button variant="ghost" className="pl-0">
              <ArrowLeft className="mr-2 h-4 w-4" /> Back to Home
            </Button>
          </Link>
          <DarkModeToggle />
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8 flex-grow">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <Book className="h-16 w-16 text-purple-600 dark:text-lime-400 mx-auto mb-4" />
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Knowledge Base
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Comprehensive guides, tutorials, and expert advice for Malaysian car owners
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            {/* Search */}
            <div className="relative w-full lg:w-96">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search knowledge base..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Filters */}
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Category Filter */}
              <div className="flex flex-wrap gap-2">
                {categories.map((category) => (
                  <Button
                    key={category}
                    variant={selectedCategory === category ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category)}
                    className="text-sm"
                  >
                    {category}
                  </Button>
                ))}
              </div>

              {/* Difficulty Filter */}
              <div className="flex flex-wrap gap-2">
                {difficulties.map((difficulty) => (
                  <Button
                    key={difficulty}
                    variant={selectedDifficulty === difficulty ? "secondary" : "outline"}
                    size="sm"
                    onClick={() => setSelectedDifficulty(difficulty)}
                    className="text-sm"
                  >
                    {difficulty}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Knowledge Items Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map((item) => (
            <Card key={item.id} className="hover:shadow-lg transition-shadow duration-300 h-full flex flex-col">
              <CardHeader className="flex-grow">
                <div className="flex items-center justify-between mb-3">
                  <div className="text-purple-600 dark:text-lime-400">
                    {item.icon}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={getDifficultyColor(item.difficulty)}>
                      {item.difficulty}
                    </Badge>
                    <div className="flex items-center text-sm text-gray-500">
                      <Clock className="h-3 w-3 mr-1" />
                      {item.readTime} min
                    </div>
                  </div>
                </div>
                
                <CardTitle className="text-lg hover:text-purple-600 dark:hover:text-lime-400 transition-colors">
                  <Link to={`/knowledge/${item.id}`}>
                    {item.title}
                  </Link>
                </CardTitle>
                
                <p className="text-gray-600 dark:text-gray-300 text-sm line-clamp-3 flex-grow">
                  {item.excerpt}
                </p>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="flex items-center justify-between mb-4">
                  <Badge variant="outline" className="text-xs">
                    {item.category}
                  </Badge>
                </div>

                <div className="flex flex-wrap gap-1 mb-4">
                  {item.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                </div>

                <Link to={`/knowledge/${item.id}`}>
                  <Button className="w-full" variant="outline">
                    Read Guide
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* No Results */}
        {filteredItems.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 dark:text-gray-400 text-lg">
              No guides found matching your criteria.
            </p>
          </div>
        )}

        {/* Call to Action */}
        <div className="mt-16 text-center">
          <Card className="max-w-2xl mx-auto">
            <CardContent className="p-8">
              <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                Need Immediate Help?
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Can't find what you're looking for? Try our AI-powered diagnostic tool for instant help.
              </p>
              <Link to="/">
                <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                  Try Diagnostic Tool
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default KnowledgeBase;
