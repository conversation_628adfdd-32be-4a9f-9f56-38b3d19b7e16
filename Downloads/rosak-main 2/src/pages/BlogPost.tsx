import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Calendar, User, Clock, Share2 } from 'lucide-react';
import Footer from '@/components/Footer';
import DarkModeToggle from '@/components/DarkModeToggle';

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  category: string;
  author: string;
  publishedAt: string;
  readTime: number;
  image?: string;
  tags: string[];
}

// Sample blog post content - in production, this would come from your database
const blogPosts: { [key: string]: BlogPost } = {
  '1': {
    id: '1',
    title: 'Top 10 Most Common Car Problems in Malaysia',
    excerpt: 'Learn about the most frequent car issues Malaysian drivers face and how to prevent them.',
    content: `
# Top 10 Most Common Car Problems in Malaysia

Malaysia's tropical climate and unique driving conditions present specific challenges for vehicle owners. Understanding these common issues can help you maintain your car better and avoid costly repairs.

## 1. Air Conditioning Problems

The hot and humid Malaysian climate puts constant strain on your car's AC system. Common issues include:

- **Refrigerant leaks**: Often caused by worn seals and gaskets
- **Compressor failure**: Due to overuse in tropical conditions
- **Clogged cabin filters**: From dust and pollution

**Prevention Tips:**
- Service your AC system annually
- Replace cabin filters every 6 months
- Use your AC regularly, even during cooler weather

## 2. Battery Drainage

High temperatures accelerate battery degradation, making battery problems extremely common in Malaysia.

**Signs of battery issues:**
- Slow engine cranking
- Dim headlights
- Dashboard warning lights

**Prevention:**
- Check battery terminals monthly for corrosion
- Test battery voltage regularly
- Replace batteries every 2-3 years in tropical climates

## 3. Tire Wear and Punctures

Malaysian roads can be challenging, with construction debris, potholes, and varying road surfaces.

**Common tire problems:**
- Uneven wear patterns
- Sidewall damage from potholes
- Punctures from road debris

**Maintenance tips:**
- Check tire pressure monthly
- Rotate tires every 10,000 km
- Inspect for cuts and bulges regularly

## 4. Engine Overheating

The combination of traffic jams and high ambient temperatures can cause engines to overheat.

**Warning signs:**
- Temperature gauge in the red zone
- Steam from under the hood
- Sweet smell (coolant leak)

**Prevention:**
- Check coolant levels monthly
- Service cooling system annually
- Don't ignore temperature warnings

## 5. Brake System Issues

Stop-and-go traffic in Malaysian cities puts extra strain on brake systems.

**Common problems:**
- Brake pad wear
- Brake fluid contamination
- Warped brake discs

**Maintenance:**
- Inspect brake pads every 6 months
- Change brake fluid every 2 years
- Address squealing or grinding immediately

## 6. Electrical Problems

Humidity and heat can cause electrical issues in vehicles.

**Common electrical problems:**
- Corroded connections
- Failed alternators
- Malfunctioning sensors

## 7. Fuel System Issues

Poor fuel quality and contamination can affect your engine's performance.

**Prevention:**
- Use reputable fuel stations
- Replace fuel filter regularly
- Use fuel additives occasionally

## 8. Suspension Problems

Malaysian road conditions can be tough on suspension systems.

**Signs of suspension issues:**
- Excessive bouncing
- Uneven tire wear
- Poor handling

## 9. Transmission Problems

Automatic transmissions can suffer in heavy traffic conditions.

**Maintenance tips:**
- Service transmission fluid regularly
- Don't ignore shifting problems
- Avoid aggressive driving in traffic

## 10. Rust and Corrosion

High humidity accelerates rust formation, especially in coastal areas.

**Prevention:**
- Wash your car regularly
- Wax every 3 months
- Address paint chips immediately

## Conclusion

Regular maintenance is key to preventing these common problems. When in doubt, consult with a qualified mechanic or use RosakMY's AI diagnostic tool for initial troubleshooting.

Remember, early detection and prevention are always more cost-effective than major repairs.
    `,
    category: 'Maintenance',
    author: 'RosakMY Team',
    publishedAt: '2024-01-15',
    readTime: 5,
    tags: ['maintenance', 'prevention', 'malaysia']
  },
  '2': {
    id: '2',
    title: 'How to Prepare Your Car for Malaysian Monsoon Season',
    excerpt: 'Essential tips to keep your vehicle safe during heavy rains and flooding.',
    content: `
# How to Prepare Your Car for Malaysian Monsoon Season

The monsoon season in Malaysia brings heavy rainfall, flooding, and challenging driving conditions. Proper preparation can keep you safe and prevent costly damage to your vehicle.

## Pre-Monsoon Vehicle Inspection

### 1. Tire Condition and Tread Depth

Good tires are crucial for wet weather driving:

- **Check tread depth**: Minimum 3mm for wet conditions
- **Inspect for cracks**: Look for sidewall damage
- **Ensure proper pressure**: Check monthly, including spare tire

### 2. Windshield and Wipers

Clear visibility is essential during heavy rain:

- **Replace worn wiper blades**: Change every 6-12 months
- **Check windshield condition**: Repair chips before they spread
- **Test washer fluid system**: Keep reservoir full

### 3. Lights and Electrical Systems

Ensure all lights work properly:

- **Headlights and taillights**: Clean lenses and replace dim bulbs
- **Hazard lights**: Test all functions
- **Interior lights**: Check dome and dashboard lights

## Flood Prevention Measures

### Engine Protection

- **Air filter inspection**: Replace if dirty or wet
- **Seal check**: Ensure engine bay seals are intact
- **Intake protection**: Know your vehicle's wading depth

### Electrical System

- **Battery terminals**: Clean and protect from corrosion
- **Fuse box**: Ensure proper sealing
- **Ground connections**: Check for corrosion

## Emergency Kit Essentials

Prepare an emergency kit for monsoon season:

### Safety Items
- Reflective triangles
- Flashlight with extra batteries
- First aid kit
- Emergency contact numbers

### Vehicle Tools
- Jumper cables
- Basic tool kit
- Tire pressure gauge
- Emergency tire sealant

### Comfort Items
- Bottled water
- Non-perishable snacks
- Blanket
- Phone charger

## Driving Tips for Wet Conditions

### Before You Drive

1. **Check weather conditions**: Avoid driving in severe weather
2. **Plan your route**: Know alternative routes
3. **Inform others**: Let someone know your travel plans

### During Heavy Rain

1. **Reduce speed**: Drive slower than normal
2. **Increase following distance**: Allow extra stopping time
3. **Use headlights**: Improve visibility for yourself and others
4. **Avoid sudden movements**: Gentle steering and braking

### Flood Situations

**Never attempt to drive through flood water**

- **Turn around**: Find an alternative route
- **If trapped**: Stay with your vehicle and call for help
- **Water depth**: Even 6 inches can cause loss of control

## Post-Flood Vehicle Care

If your vehicle has been exposed to flood water:

### Immediate Actions

1. **Don't start the engine**: Water may have entered critical systems
2. **Disconnect battery**: Prevent electrical damage
3. **Document damage**: Take photos for insurance

### Professional Inspection

Have a qualified mechanic check:

- **Engine oil**: Look for water contamination
- **Transmission fluid**: Check for water intrusion
- **Brake system**: Ensure proper function
- **Electrical systems**: Test all components

## Insurance Considerations

### Before Monsoon Season

- **Review coverage**: Understand what's covered
- **Document condition**: Take photos of your vehicle
- **Keep receipts**: Maintain records of maintenance

### Filing Claims

- **Report promptly**: Contact insurer immediately
- **Provide documentation**: Photos and maintenance records
- **Follow procedures**: Comply with insurer requirements

## Maintenance Schedule

### Monthly Checks
- Tire pressure and condition
- Fluid levels
- Light functionality
- Wiper blade condition

### Seasonal Preparation
- Professional inspection
- Fluid changes if needed
- Emergency kit update
- Route planning

## Conclusion

Proper preparation for monsoon season can prevent accidents and costly repairs. Regular maintenance, emergency preparedness, and safe driving practices are your best defense against Malaysia's challenging wet weather conditions.

Remember, no journey is so urgent that it's worth risking your safety in severe weather conditions.
    `,
    category: 'Seasonal Care',
    author: 'RosakMY Team',
    publishedAt: '2024-01-10',
    readTime: 7,
    tags: ['monsoon', 'weather', 'safety']
  },
  '3': {
    id: '3',
    title: 'Understanding Your Car\'s Warning Lights',
    excerpt: 'A comprehensive guide to dashboard warning lights and what they mean.',
    content: `
# Understanding Your Car's Warning Lights

Your car's dashboard is equipped with various warning lights designed to alert you to potential problems. Understanding these lights can help you take appropriate action and prevent costly repairs.

## Critical Warning Lights (Stop Immediately)

### 1. Engine Temperature Warning
**Symbol:** Thermometer or temperature gauge in red
**Meaning:** Engine is overheating
**Action:** Stop driving immediately, turn off engine, wait for cooling

**Causes:**
- Low coolant levels
- Faulty thermostat
- Radiator problems
- Water pump failure

### 2. Oil Pressure Warning
**Symbol:** Oil can or "OIL" text
**Meaning:** Low oil pressure or oil level
**Action:** Stop engine immediately, check oil level

**Consequences of ignoring:**
- Complete engine failure
- Expensive engine rebuild
- Permanent damage to engine components

### 3. Battery/Charging System Warning
**Symbol:** Battery symbol
**Meaning:** Charging system malfunction
**Action:** Drive to nearest service center immediately

## Important Warning Lights (Address Soon)

### 4. Check Engine Light
**Symbol:** Engine outline or "CHECK ENGINE"
**Meaning:** Engine management system detected a problem
**Action:** Have diagnosed within a few days

**Common causes:**
- Faulty oxygen sensor
- Loose gas cap
- Catalytic converter issues
- Mass airflow sensor problems

### 5. ABS Warning Light
**Symbol:** "ABS" text in circle
**Meaning:** Anti-lock braking system malfunction
**Action:** Drive carefully, have checked soon

### 6. Airbag Warning Light
**Symbol:** Person with airbag deploying
**Meaning:** Airbag system malfunction
**Action:** Have diagnosed immediately for safety

## Maintenance Reminder Lights

### 7. Service Required Light
**Symbol:** Wrench or "SERVICE"
**Meaning:** Scheduled maintenance due
**Action:** Schedule service appointment

### 8. Tire Pressure Warning
**Symbol:** Tire cross-section with exclamation mark
**Meaning:** One or more tires have low pressure
**Action:** Check and adjust tire pressure

## Malaysian-Specific Considerations

### Hot Climate Effects
- Temperature warnings more common
- Battery lights frequent due to heat
- AC system warnings during hot weather

### Monsoon Season Alerts
- Increased electrical system warnings
- More frequent tire pressure alerts
- Brake system warnings after flooding

## What to Do When Warning Lights Appear

### Immediate Actions
1. **Don't panic** - Most warnings aren't emergencies
2. **Note the light** - Remember which light appeared
3. **Check your manual** - Refer to owner's manual
4. **Assess driving conditions** - Can you safely continue?

### When to Stop Immediately
- Red warning lights
- Temperature warnings
- Oil pressure warnings
- Brake system warnings

### When You Can Continue Driving
- Yellow/amber lights (with caution)
- Service reminder lights
- Non-critical system warnings

## Prevention Tips

### Regular Maintenance
- Follow service schedules
- Check fluid levels monthly
- Monitor tire pressure
- Replace worn components promptly

### Malaysian Climate Considerations
- More frequent coolant checks
- Battery testing every 6 months
- AC system maintenance
- Monsoon preparation checks

## Using RosakMY for Warning Lights

When warning lights appear:
1. Describe the light symbol to our AI
2. Mention any symptoms you've noticed
3. Include your car model and year
4. Follow our safety recommendations

## Conclusion

Dashboard warning lights are your car's way of communicating problems. Understanding these signals and taking appropriate action can save you money and prevent dangerous situations. When in doubt, consult a professional mechanic or use RosakMY's diagnostic tool for guidance.

Remember: It's always better to be cautious with warning lights than to ignore them and face expensive repairs later.
    `,
    category: 'Troubleshooting',
    author: 'RosakMY Team',
    publishedAt: '2024-01-05',
    readTime: 8,
    tags: ['dashboard', 'warning-lights', 'diagnostics']
  },
  '7': {
    id: '7',
    title: 'Proton Car Maintenance: Essential Tips for Malaysian Owners',
    excerpt: 'Comprehensive maintenance guide specifically for Proton vehicles in Malaysian conditions.',
    content: `
# Proton Car Maintenance: Essential Tips for Malaysian Owners

Proton vehicles are designed for Malaysian roads and climate, but proper maintenance is crucial for optimal performance and longevity. Here's your complete guide to keeping your Proton running smoothly.

## Understanding Your Proton Model

### Popular Proton Models in Malaysia
- **Proton X50** - Compact SUV with advanced features
- **Proton X70** - Premium SUV with intelligent connectivity
- **Proton Saga** - Malaysia's iconic sedan
- **Proton Persona** - Spacious family sedan
- **Proton Iriz** - Compact hatchback for city driving

## Essential Maintenance Schedule

### Every 5,000 km or 3 months
- **Engine oil change** - Use 5W-30 or 10W-30 for Malaysian climate
- **Oil filter replacement**
- **Basic inspection** - lights, wipers, fluid levels

### Every 10,000 km or 6 months
- **Air filter inspection** - Replace if dirty (important in dusty conditions)
- **Cabin filter replacement** - Essential for AC efficiency
- **Tire rotation and pressure check**
- **Battery terminals cleaning**

### Every 20,000 km or 12 months
- **Spark plugs inspection** (replace if needed)
- **Brake fluid check**
- **Coolant system inspection**
- **Transmission fluid check**

## Malaysian Climate Considerations

### Hot Weather Maintenance
- **Cooling System**: Check coolant levels monthly
- **AC System**: Service annually, replace cabin filter every 6 months
- **Battery**: Test every 6 months (heat reduces battery life)
- **Tires**: Check pressure weekly (heat causes expansion)

### Monsoon Season Preparation
- **Brake System**: Ensure optimal performance for wet conditions
- **Wipers**: Replace before monsoon season
- **Lights**: Check all bulbs for visibility
- **Drainage**: Clear sunroof and door drains

## Proton-Specific Tips

### Engine Care
- **Use recommended oil grade**: Follow Proton's specifications
- **Warm-up period**: 30 seconds in Malaysian climate is sufficient
- **Regular driving**: Don't let the car sit idle for weeks
- **Quality fuel**: Use reputable petrol stations

### Transmission Maintenance
- **CVT models**: Follow Proton's CVT fluid change intervals
- **Manual transmission**: Check clutch operation regularly
- **Automatic**: Service transmission fluid every 40,000 km

### Electrical System
- **Infotainment updates**: Keep software updated for X50/X70
- **Sensor cleaning**: Clean parking sensors and cameras monthly
- **Wiring inspection**: Check for rodent damage (common in Malaysia)

## Common Proton Issues and Prevention

### Air Conditioning Problems
**Symptoms**: Weak cooling, strange odors
**Prevention**:
- Run AC for 10 minutes weekly
- Replace cabin filter regularly
- Annual AC service

### Engine Overheating
**Symptoms**: Temperature gauge rising, steam from engine
**Prevention**:
- Regular coolant checks
- Radiator cleaning
- Thermostat inspection

### Electrical Issues
**Symptoms**: Warning lights, starting problems
**Prevention**:
- Battery maintenance
- Regular alternator checks
- Protect wiring from moisture

## DIY Maintenance You Can Do

### Weekly Checks
- Engine oil level
- Coolant level
- Brake fluid level
- Tire pressure
- Lights functionality

### Monthly Tasks
- Battery terminals cleaning
- Air filter inspection
- Wiper blade condition
- Exterior and interior cleaning

### What to Leave to Professionals
- Engine diagnostics
- Transmission service
- Brake system work
- Electrical troubleshooting
- Warranty-related repairs

## Cost-Saving Tips

### Genuine vs Aftermarket Parts
- **Critical components**: Use genuine Proton parts
- **Wear items**: Quality aftermarket acceptable (filters, wipers)
- **Warranty period**: Always use genuine parts

### Service Centers
- **Authorized dealers**: For warranty and complex issues
- **Trusted workshops**: For routine maintenance
- **DIY maintenance**: For simple checks and cleaning

## Seasonal Maintenance Calendar

### January-March (Hot Season)
- AC system check
- Coolant system inspection
- Battery testing
- Tire condition assessment

### April-June (Pre-Monsoon)
- Wiper blade replacement
- Brake system check
- Light bulb inspection
- Drainage system cleaning

### July-September (Monsoon)
- Regular car washing (remove salt/dirt)
- Brake performance monitoring
- Electrical system protection
- Interior moisture control

### October-December (Post-Monsoon)
- Comprehensive inspection
- Rust prevention treatment
- Engine bay cleaning
- Preparation for next year

## Warning Signs to Watch For

### Immediate Attention Required
- Engine temperature warning
- Oil pressure light
- Brake warning light
- Strange noises from engine

### Schedule Service Soon
- Reduced fuel efficiency
- Rough idling
- AC not cooling properly
- Unusual vibrations

## Conclusion

Regular maintenance is the key to keeping your Proton reliable and efficient in Malaysian conditions. Follow the recommended schedule, address issues promptly, and don't hesitate to consult professionals when needed.

Remember: Prevention is always cheaper than repair, especially in Malaysia's challenging climate conditions.
    `,
    category: 'Maintenance',
    author: 'RosakMY Team',
    publishedAt: '2024-01-20',
    readTime: 8,
    tags: ['proton', 'maintenance', 'malaysian-cars']
  }
};

const BlogPost = () => {
  const { id } = useParams<{ id: string }>();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (id && blogPosts[id]) {
      setPost(blogPosts[id]);
    }
    setLoading(false);
  }, [id]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-MY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: post?.title,
        text: post?.excerpt,
        url: window.location.href,
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      alert('Link copied to clipboard!');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-300">Loading article...</p>
        </div>
      </div>
    );
  }

  if (!post) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">Article Not Found</h1>
          <p className="text-gray-600 dark:text-gray-300 mb-6">The article you're looking for doesn't exist.</p>
          <Link to="/blog">
            <Button>Back to Blog</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 flex flex-col">
      {/* Header */}
      <header className="border-b border-gray-100 dark:border-gray-800 py-4 px-4">
        <div className="container mx-auto flex items-center justify-between">
          <Link to="/blog">
            <Button variant="ghost" className="pl-0">
              <ArrowLeft className="mr-2 h-4 w-4" /> Back to Blog
            </Button>
          </Link>
          <DarkModeToggle />
        </div>
      </header>

      {/* Article Content */}
      <article className="container mx-auto px-4 py-8 max-w-4xl flex-grow">
        {/* Article Header */}
        <header className="mb-8">
          <div className="flex items-center gap-2 mb-4">
            <Badge variant="secondary">{post.category}</Badge>
            <div className="flex items-center text-sm text-gray-500">
              <Clock className="h-3 w-3 mr-1" />
              {post.readTime} min read
            </div>
          </div>

          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {post.title}
          </h1>

          <p className="text-xl text-gray-600 dark:text-gray-300 mb-6">
            {post.excerpt}
          </p>

          <div className="flex items-center justify-between border-b border-gray-200 dark:border-gray-700 pb-6">
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <div className="flex items-center">
                <User className="h-4 w-4 mr-1" />
                {post.author}
              </div>
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                {formatDate(post.publishedAt)}
              </div>
            </div>

            <Button variant="outline" size="sm" onClick={handleShare}>
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
          </div>
        </header>

        {/* Article Body */}
        <div className="prose prose-lg dark:prose-invert max-w-none">
          <div 
            dangerouslySetInnerHTML={{ 
              __html: post.content.replace(/\n/g, '<br>').replace(/#{1,6}\s/g, (match) => {
                const level = match.trim().length;
                return `<h${level} class="text-${4-level}xl font-bold mt-8 mb-4">`;
              }).replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            }} 
          />
        </div>

        {/* Tags */}
        <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <Badge key={tag} variant="outline">
                #{tag}
              </Badge>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="mt-12 p-6 bg-purple-50 dark:bg-gray-800 rounded-lg text-center">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Need Help with Your Car?
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-4">
            Use our AI-powered diagnostic tool to get instant help with your car problems.
          </p>
          <Link to="/">
            <Button className="bg-purple-600 hover:bg-purple-700 text-white">
              Try RosakMY Diagnostic Tool
            </Button>
          </Link>
        </div>
      </article>

      <Footer />
    </div>
  );
};

export default BlogPost;
