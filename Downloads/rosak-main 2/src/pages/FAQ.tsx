import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ArrowLeft, ChevronDown, ChevronRight, HelpCircle, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import Footer from '@/components/Footer';
import DarkModeToggle from '@/components/DarkModeToggle';

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
}

const faqData: FAQItem[] = [
  {
    id: '1',
    question: 'How accurate are RosakMY\'s diagnostic results?',
    answer: 'RosakMY uses advanced AI technology powered by Perplexity AI to analyze your car symptoms and provide diagnostic suggestions. While our system is highly sophisticated and draws from extensive automotive knowledge, the results should be considered as helpful guidance rather than definitive diagnoses. We always recommend consulting with a qualified mechanic for accurate diagnosis and professional repair work, especially for safety-critical issues.',
    category: 'Diagnostics'
  },
  {
    id: '2',
    question: 'Is RosakM<PERSON> free to use?',
    answer: 'Yes, RosakM<PERSON>\'s basic diagnostic tool is completely free to use. You can describe your car problems and receive AI-powered suggestions without any cost. We may introduce premium features in the future that offer enhanced functionality, but our core diagnostic service will remain free for all Malaysian drivers.',
    category: 'Pricing'
  },
  {
    id: '3',
    question: 'What types of car problems can RosakMY help with?',
    answer: 'RosakMY can help with a wide range of automotive issues including engine problems, electrical issues, brake concerns, air conditioning problems, transmission issues, suspension problems, and more. Our AI is trained to understand various symptoms like strange noises, warning lights, performance issues, and unusual smells. However, we specialize in common problems that Malaysian drivers typically encounter.',
    category: 'Diagnostics'
  },
  {
    id: '4',
    question: 'Do I need to create an account to use RosakMY?',
    answer: 'No, you can use our basic diagnostic tool without creating an account. However, creating a free account allows you to save your search history, bookmark favorite car models, and access personalized recommendations. Account holders also get priority access to new features as they become available.',
    category: 'Account'
  },
  {
    id: '5',
    question: 'Can RosakMY recommend mechanics in my area?',
    answer: 'We\'re currently working on integrating local mechanic recommendations into our platform. This feature will be available soon in major Malaysian cities including Kuala Lumpur, Penang, Johor Bahru, and others. In the meantime, we provide general guidance on when to seek professional help and what type of specialist you might need.',
    category: 'Services'
  },
  {
    id: '6',
    question: 'Is my data safe with RosakMY?',
    answer: 'Yes, we take data privacy and security very seriously. All diagnostic queries are encrypted and stored securely. We only collect information necessary to provide our services and improve our AI algorithms. We never sell your personal data to third parties. Please review our Privacy Policy for detailed information about how we handle your data.',
    category: 'Privacy'
  },
  {
    id: '7',
    question: 'What should I do if RosakMY suggests my car is unsafe to drive?',
    answer: 'If our diagnostic tool indicates a potential safety issue, stop driving immediately and seek professional help. Safety warnings should never be ignored. Contact a qualified mechanic, your car dealership, or roadside assistance service. Some symptoms like brake problems, steering issues, or engine overheating can be dangerous and require immediate attention.',
    category: 'Safety'
  },
  {
    id: '8',
    question: 'Can RosakMY help with motorcycle problems?',
    answer: 'Currently, RosakMY is optimized for passenger cars and light commercial vehicles. While some general automotive principles apply to motorcycles, we recommend consulting motorcycle-specific resources or mechanics for accurate motorcycle diagnostics. We may expand to include motorcycle support in future updates.',
    category: 'Vehicle Types'
  },
  {
    id: '9',
    question: 'How often should I use RosakMY for car maintenance?',
    answer: 'RosakMY is designed to help when you notice problems or symptoms with your car. For regular maintenance, follow your vehicle manufacturer\'s recommended service schedule. However, you can use our blog and knowledge base for maintenance tips and seasonal car care advice. We also provide guidance on preventive maintenance to help avoid common problems.',
    category: 'Maintenance'
  },
  {
    id: '10',
    question: 'Does RosakMY work for all car brands?',
    answer: 'Yes, RosakMY can help with problems across all major car brands available in Malaysia, including Proton, Perodua, Toyota, Honda, Nissan, Mazda, BMW, Mercedes-Benz, and many others. Our AI is trained on general automotive principles that apply to most vehicles, though specific model information can help provide more targeted advice.',
    category: 'Vehicle Types'
  },
  {
    id: '11',
    question: 'What if RosakMY can\'t identify my car problem?',
    answer: 'If our AI cannot provide a clear diagnosis, we\'ll suggest general troubleshooting steps and recommend consulting a professional mechanic. Some complex or rare problems may require hands-on inspection and specialized diagnostic equipment. You can also try rephrasing your symptoms or providing additional details for better results.',
    category: 'Diagnostics'
  },
  {
    id: '12',
    question: 'Can I use RosakMY in Bahasa Malaysia?',
    answer: 'We\'re currently working on full Bahasa Malaysia support, which will be available soon. For now, RosakMY works best in English, but you can describe your problems using simple terms or a mix of English and Bahasa Malaysia. Our AI is designed to understand various ways of describing car problems.',
    category: 'Language'
  }
];

const categories = ['All', 'Diagnostics', 'Pricing', 'Account', 'Services', 'Privacy', 'Safety', 'Vehicle Types', 'Maintenance', 'Language'];

const FAQ = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [openItems, setOpenItems] = useState<string[]>([]);

  const filteredFAQs = faqData.filter(faq => {
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'All' || faq.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const toggleItem = (id: string) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 flex flex-col">
      {/* Header */}
      <header className="border-b border-gray-100 dark:border-gray-800 py-4 px-4">
        <div className="container mx-auto flex items-center justify-between">
          <Link to="/">
            <Button variant="ghost" className="pl-0">
              <ArrowLeft className="mr-2 h-4 w-4" /> Back to Home
            </Button>
          </Link>
          <DarkModeToggle />
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8 flex-grow">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <HelpCircle className="h-16 w-16 text-purple-600 dark:text-lime-400 mx-auto mb-4" />
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Frequently Asked Questions
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Find answers to common questions about RosakMY's AI-powered vehicle diagnostics
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between max-w-4xl mx-auto">
            {/* Search */}
            <div className="relative w-full md:w-96">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search FAQs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Category Filters */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className="text-sm"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* FAQ Items */}
        <div className="max-w-4xl mx-auto">
          {filteredFAQs.length > 0 ? (
            <div className="space-y-4">
              {filteredFAQs.map((faq) => (
                <Card key={faq.id} className="overflow-hidden">
                  <Collapsible
                    open={openItems.includes(faq.id)}
                    onOpenChange={() => toggleItem(faq.id)}
                  >
                    <CollapsibleTrigger asChild>
                      <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors">
                        <div className="flex items-center justify-between">
                          <div className="flex items-start space-x-3">
                            <div className="flex-shrink-0">
                              {openItems.includes(faq.id) ? (
                                <ChevronDown className="h-5 w-5 text-purple-600 dark:text-lime-400 mt-1" />
                              ) : (
                                <ChevronRight className="h-5 w-5 text-purple-600 dark:text-lime-400 mt-1" />
                              )}
                            </div>
                            <div className="flex-grow">
                              <CardTitle className="text-left text-lg font-semibold text-gray-900 dark:text-white">
                                {faq.question}
                              </CardTitle>
                              <p className="text-sm text-purple-600 dark:text-lime-400 mt-1">
                                {faq.category}
                              </p>
                            </div>
                          </div>
                        </div>
                      </CardHeader>
                    </CollapsibleTrigger>
                    <CollapsibleContent>
                      <CardContent className="pt-0">
                        <div className="pl-8">
                          <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                            {faq.answer}
                          </p>
                        </div>
                      </CardContent>
                    </CollapsibleContent>
                  </Collapsible>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400 text-lg">
                No FAQs found matching your search criteria.
              </p>
            </div>
          )}
        </div>

        {/* Contact Section */}
        <div className="mt-16 text-center">
          <Card className="max-w-2xl mx-auto">
            <CardContent className="p-8">
              <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
                Still have questions?
              </h3>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Can't find the answer you're looking for? Our support team is here to help.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/contact">
                  <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                    Contact Support
                  </Button>
                </Link>
                <Link to="/">
                  <Button variant="outline">
                    Try Diagnostic Tool
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default FAQ;
