import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import SearchBox from '@/components/SearchBox';
import ResultsDisplay from '@/components/ResultsDisplay';
import ErrorMessage from '@/components/ErrorMessage';
import Footer from '@/components/Footer';
import { fetchDiagnostic } from '@/utils/api';
import { getSuggestions, Suggestion } from '@/utils/autoSuggestions';
import { toast } from "@/components/ui/use-toast";
import DarkModeToggle from '@/components/DarkModeToggle';
import UserDashboard from '@/components/UserDashboard';
import { useUserPreferences } from '@/context/UserPreferencesContext';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { ShieldCheck } from 'lucide-react';

const Index = () => {
  const location = useLocation();
  const [searchQuery, setSearchQuery] = useState('');
  const [carModel, setCarModel] = useState('');
  const [result, setResult] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showErrorMessage, setShowErrorMessage] = useState(false);
  const [errorMessageText, setErrorMessageText] = useState('');
  const [isAdmin, setIsAdmin] = useState(false);
  const { user, signOut } = useAuth();
  const { addRecentSearch } = useUserPreferences();

  // Check if user is admin
  useEffect(() => {
    const checkAdminStatus = async () => {
      if (!user) {
        setIsAdmin(false);
        return;
      }
      
      try {
        const { data, error } = await supabase.rpc('is_admin');
        
        if (error) throw error;
        setIsAdmin(!!data);
      } catch (error) {
        console.error('Error checking admin status:', error);
        setIsAdmin(false);
      }
    };
    
    checkAdminStatus();
  }, [user]);

  // Handle car model and search selection from dashboard
  useEffect(() => {
    if (location.state) {
      if (location.state.selectedCarModel) {
        setCarModel(location.state.selectedCarModel);
      }
      if (location.state.selectedQuery) {
        setSearchQuery(location.state.selectedQuery);
      }
      // Clear the state to prevent re-applying on refresh
      window.history.replaceState({}, document.title);
    }
  }, [location.state]);

  const handleSearch = async (query: string, carModel?: string) => {
    if (!query.trim()) {
      setErrorMessageText('Please enter a car problem to diagnose');
      setShowErrorMessage(true);
      return;
    }

    setIsLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetchDiagnostic(query, carModel);
      setResult(response);
      
      // Add to recent searches if user is logged in
      if (user) {
        addRecentSearch({ query, carModel });
      }
    } catch (err) {
      setError('Failed to get diagnostic. Please try again.');
      console.error('Search error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSelectSearch = (query: string, carModel?: string) => {
    setSearchQuery(query);
    if (carModel) {
      setCarModel(carModel);
    }
  };

  const handleSelectCarModel = (selectedCarModel: string) => {
    setCarModel(selectedCarModel);
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 flex flex-col">
      <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48Y2lyY2xlIGN4PSIyIiBjeT0iMiIgcj0iMSIgZmlsbD0iIzY2NjY2NiIgZmlsbC1vcGFjaXR5PSIwLjMiLz48L3N2Zz4=')] dark:bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHZpZXdCb3g9IjAgMCAyMCAyMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48Y2lyY2xlIGN4PSIyIiBjeT0iMiIgcj0iMSIgZmlsbD0iI2ZmZmZmZiIgZmlsbC1vcGFjaXR5PSIwLjE1Ii8+PC9zdmc+')] opacity-100 [mask-image:linear-gradient(to_bottom,transparent,black,transparent)]"></div>
      
      <header className="flex justify-between pt-6 px-8 w-full absolute top-0 z-10">
        <div className="flex items-center space-x-6">
          <Link to="/">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white hover:text-purple-600 dark:hover:text-lime-400 transition-colors">
              RosakMY
            </h1>
          </Link>

          {/* Navigation Menu */}
          <nav className="hidden md:flex items-center space-x-4">
            <Link to="/blog" className="text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-lime-400 text-sm font-medium transition-colors">
              Blog
            </Link>
            <Link to="/knowledge" className="text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-lime-400 text-sm font-medium transition-colors">
              Guides
            </Link>
            <Link to="/about" className="text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-lime-400 text-sm font-medium transition-colors">
              About
            </Link>
            <Link to="/faq" className="text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-lime-400 text-sm font-medium transition-colors">
              FAQ
            </Link>
            <Link to="/contact" className="text-gray-600 dark:text-gray-300 hover:text-purple-600 dark:hover:text-lime-400 text-sm font-medium transition-colors">
              Contact
            </Link>
          </nav>

          <UserDashboard
            onSelectSearch={handleSelectSearch}
            onSelectCarModel={handleSelectCarModel}
          />
        </div>
        <div className="flex items-center space-x-4">
          <DarkModeToggle />
          {user ? (
            <div className="flex items-center space-x-2">
              {isAdmin && (
                <Link to="/admin">
                  <Button variant="outline" className="flex items-center space-x-1 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 text-sm">
                    <ShieldCheck className="h-4 w-4 text-amber-500" />
                    <span>Admin</span>
                  </Button>
                </Link>
              )}
              <Link to="/dashboard">
                <Button variant="outline" className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 text-sm">
                  Dashboard
                </Button>
              </Link>
              <Button 
                onClick={() => signOut()}
                variant="ghost" 
                className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 text-sm"
              >
                Logout
              </Button>
            </div>
          ) : (
            <div className="flex items-center space-x-2">
              <Link to="/auth">
                <Button 
                  variant="outline"
                  className="text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-gray-100 text-sm"
                >
                  Login
                </Button>
              </Link>
              <Link to="/auth?tab=signup">
                <Button 
                  className="bg-purple-600 dark:bg-lime-500 text-white text-sm px-3 py-1 rounded-md hover:bg-purple-700 dark:hover:bg-lime-600 transition-colors"
                >
                  Sign Up
                </Button>
              </Link>
            </div>
          )}
        </div>
      </header>
      
      <div className="flex-grow flex items-center justify-center">
        <div className="container mx-auto px-4 relative w-full max-w-3xl">
          <SearchBox 
            onSearch={handleSearch} 
            isSearching={isLoading} 
            initialQuery={searchQuery}
            initialCarModel={carModel}
          />
          
          {(result || isLoading || error) && (
            <div className="mt-8">
              <ResultsDisplay 
                result={result} 
                isLoading={isLoading} 
                error={error} 
              />
            </div>
          )}
        </div>
      </div>

      {showErrorMessage && (
        <ErrorMessage 
          message={errorMessageText}
          onDismiss={() => setShowErrorMessage(false)} 
        />
      )}
      
      <Footer />
    </div>
  );
};

export default Index;
