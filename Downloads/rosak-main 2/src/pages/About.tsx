import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ArrowLeft, Target, Users, Zap, Shield, Heart, Globe } from 'lucide-react';
import Footer from '@/components/Footer';
import DarkModeToggle from '@/components/DarkModeToggle';

const About = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 flex flex-col">
      {/* Header */}
      <header className="border-b border-gray-100 dark:border-gray-800 py-4 px-4">
        <div className="container mx-auto flex items-center justify-between">
          <Link to="/">
            <Button variant="ghost" className="pl-0">
              <ArrowLeft className="mr-2 h-4 w-4" /> Back to Home
            </Button>
          </Link>
          <DarkModeToggle />
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8 flex-grow">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            About RosakMY
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Empowering Malaysian drivers with AI-powered vehicle diagnostics and expert automotive knowledge
          </p>
        </div>

        {/* Our Story */}
        <section className="mb-12">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6 text-center">
              Our Story
            </h2>
            <div className="prose prose-lg dark:prose-invert mx-auto">
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
                RosakMY was born from a simple observation: most Malaysian drivers struggle to understand what's wrong with their vehicles. Whether it's a strange noise, an unusual smell, or a dashboard warning light, car problems can be confusing, stressful, and expensive to diagnose.
              </p>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
                Founded in 2024, we set out to democratize automotive knowledge by combining artificial intelligence with expert automotive insights. Our platform makes car diagnostics as simple as having a conversation with a knowledgeable friend – no technical jargon, no complicated procedures, just clear, actionable advice.
              </p>
              <p className="text-gray-600 dark:text-gray-300 leading-relaxed">
                Today, RosakMY serves thousands of Malaysian drivers, helping them make informed decisions about their vehicles while saving time and money on unnecessary workshop visits.
              </p>
            </div>
          </div>
        </section>

        {/* Mission & Vision */}
        <section className="mb-12">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card className="text-center">
              <CardHeader>
                <Target className="h-12 w-12 text-purple-600 dark:text-lime-400 mx-auto mb-4" />
                <CardTitle className="text-2xl">Our Mission</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300">
                  To make automotive diagnostics accessible, affordable, and understandable for every Malaysian driver, regardless of their technical knowledge or experience.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Globe className="h-12 w-12 text-purple-600 dark:text-lime-400 mx-auto mb-4" />
                <CardTitle className="text-2xl">Our Vision</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300">
                  To become Malaysia's most trusted automotive companion, empowering drivers with knowledge and confidence to maintain their vehicles safely and efficiently.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Our Values */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Our Values
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="text-center">
              <CardHeader>
                <Shield className="h-10 w-10 text-purple-600 dark:text-lime-400 mx-auto mb-3" />
                <CardTitle className="text-lg">Safety First</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  We prioritize your safety above all else, providing clear warnings and recommendations for potentially dangerous situations.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Users className="h-10 w-10 text-purple-600 dark:text-lime-400 mx-auto mb-3" />
                <CardTitle className="text-lg">Accessibility</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  Automotive knowledge should be available to everyone, regardless of technical background or economic status.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <Heart className="h-10 w-10 text-purple-600 dark:text-lime-400 mx-auto mb-3" />
                <CardTitle className="text-lg">Empathy</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300 text-sm">
                  We understand the stress and uncertainty that comes with car problems and strive to provide reassuring, helpful guidance.
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* What Makes Us Different */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            What Makes Us Different
          </h2>
          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="flex items-start space-x-4">
                <Zap className="h-8 w-8 text-purple-600 dark:text-lime-400 flex-shrink-0 mt-1" />
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    AI-Powered Diagnostics
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    Our advanced AI system, powered by Perplexity AI, provides instant, accurate diagnostics based on your symptoms description.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <Globe className="h-8 w-8 text-purple-600 dark:text-lime-400 flex-shrink-0 mt-1" />
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    Malaysian-Focused
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    We understand Malaysia's unique driving conditions, climate challenges, and local automotive landscape.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <Users className="h-8 w-8 text-purple-600 dark:text-lime-400 flex-shrink-0 mt-1" />
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    User-Friendly Interface
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    No technical jargon or complicated forms – just describe your problem in plain language and get clear answers.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <Shield className="h-8 w-8 text-purple-600 dark:text-lime-400 flex-shrink-0 mt-1" />
                <div>
                  <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                    Safety-Conscious
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    We always prioritize your safety, clearly indicating when professional help is needed and when it's safe to drive.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Technology Stack */}
        <section className="mb-12">
          <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            Our Technology
          </h2>
          <div className="max-w-4xl mx-auto">
            <Card>
              <CardContent className="p-8">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                      Frontend Technology
                    </h3>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                      <li>• React 18 with TypeScript</li>
                      <li>• Tailwind CSS for responsive design</li>
                      <li>• Vite for fast development</li>
                      <li>• Progressive Web App capabilities</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                      Backend & AI
                    </h3>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-300">
                      <li>• Perplexity AI for diagnostics</li>
                      <li>• Supabase for data management</li>
                      <li>• Edge functions for performance</li>
                      <li>• Real-time data synchronization</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Contact Information */}
        <section className="mb-12">
          <div className="text-center">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
              Get in Touch
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
              Have questions, suggestions, or feedback? We'd love to hear from you. Our team is committed to continuously improving RosakMY based on user needs and experiences.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/contact">
                <Button className="bg-purple-600 hover:bg-purple-700 text-white">
                  Contact Us
                </Button>
              </Link>
              <Link to="/">
                <Button variant="outline">
                  Try Our Diagnostic Tool
                </Button>
              </Link>
            </div>
          </div>
        </section>
      </div>

      <Footer />
    </div>
  );
};

export default About;
