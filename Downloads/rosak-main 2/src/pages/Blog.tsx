import React, { useState, useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { ArrowLeft, Search, Calendar, User, Clock } from 'lucide-react';
import Footer from '@/components/Footer';
import DarkModeToggle from '@/components/DarkModeToggle';

interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  category: string;
  author: string;
  publishedAt: string;
  readTime: number;
  image?: string;
  tags: string[];
}

// Sample blog posts - in production, these would come from your database
const samplePosts: BlogPost[] = [
  {
    id: '1',
    title: 'Top 10 Most Common Car Problems in Malaysia',
    excerpt: 'Learn about the most frequent car issues Malaysian drivers face and how to prevent them.',
    content: '',
    category: 'Maintenance',
    author: 'RosakMY Team',
    publishedAt: '2024-01-15',
    readTime: 5,
    tags: ['maintenance', 'prevention', 'malaysia']
  },
  {
    id: '2',
    title: 'How to Prepare Your Car for Malaysian Monsoon Season',
    excerpt: 'Essential tips to keep your vehicle safe during heavy rains and flooding.',
    content: '',
    category: 'Seasonal Care',
    author: 'RosakMY Team',
    publishedAt: '2024-01-10',
    readTime: 7,
    tags: ['monsoon', 'weather', 'safety']
  },
  {
    id: '3',
    title: 'Understanding Your Car\'s Warning Lights',
    excerpt: 'A comprehensive guide to dashboard warning lights and what they mean.',
    content: '',
    category: 'Troubleshooting',
    author: 'RosakMY Team',
    publishedAt: '2024-01-05',
    readTime: 8,
    tags: ['dashboard', 'warning-lights', 'diagnostics']
  },
  {
    id: '4',
    title: 'DIY Car Maintenance: What You Can Do at Home',
    excerpt: 'Simple maintenance tasks every Malaysian car owner can perform themselves.',
    content: '',
    category: 'DIY',
    author: 'RosakMY Team',
    publishedAt: '2024-01-01',
    readTime: 6,
    tags: ['diy', 'maintenance', 'home-repair']
  },
  {
    id: '5',
    title: 'Choosing the Right Motor Oil for Malaysian Climate',
    excerpt: 'Everything you need to know about selecting motor oil for tropical conditions.',
    content: '',
    category: 'Maintenance',
    author: 'RosakMY Team',
    publishedAt: '2023-12-28',
    readTime: 4,
    tags: ['motor-oil', 'climate', 'tropical']
  },
  {
    id: '6',
    title: 'Electric vs Hybrid vs Petrol Cars in Malaysia',
    excerpt: 'Compare different vehicle types and their suitability for Malaysian roads.',
    content: '',
    category: 'Car Buying',
    author: 'RosakMY Team',
    publishedAt: '2023-12-25',
    readTime: 10,
    tags: ['electric', 'hybrid', 'comparison']
  },
  {
    id: '7',
    title: 'Proton Car Maintenance: Essential Tips for Malaysian Owners',
    excerpt: 'Comprehensive maintenance guide specifically for Proton vehicles in Malaysian conditions.',
    content: '',
    category: 'Maintenance',
    author: 'RosakMY Team',
    publishedAt: '2024-01-20',
    readTime: 8,
    tags: ['proton', 'maintenance', 'malaysian-cars']
  }
];

const categories = ['All', 'Maintenance', 'Troubleshooting', 'DIY', 'Seasonal Care', 'Car Buying'];

const Blog = () => {
  const [posts, setPosts] = useState<BlogPost[]>(samplePosts);
  const [filteredPosts, setFilteredPosts] = useState<BlogPost[]>(samplePosts);
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');

  useEffect(() => {
    let filtered = posts;

    // Filter by category
    if (selectedCategory !== 'All') {
      filtered = filtered.filter(post => post.category === selectedCategory);
    }

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    setFilteredPosts(filtered);
  }, [posts, selectedCategory, searchQuery]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-MY', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 flex flex-col">
      {/* Header */}
      <header className="border-b border-gray-100 dark:border-gray-800 py-4 px-4">
        <div className="container mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link to="/">
              <Button variant="ghost" className="pl-0">
                <ArrowLeft className="mr-2 h-4 w-4" /> Back to Home
              </Button>
            </Link>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              RosakMY Blog
            </h1>
          </div>
          <DarkModeToggle />
        </div>
      </header>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8 flex-grow">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            Car Care Knowledge Hub
          </h2>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Expert advice, maintenance tips, and troubleshooting guides for Malaysian car owners
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            {/* Search */}
            <div className="relative w-full md:w-96">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                type="text"
                placeholder="Search articles..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Category Filters */}
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className="text-sm"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Blog Posts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredPosts.map((post) => (
            <Card key={post.id} className="hover:shadow-lg transition-shadow duration-300">
              <CardHeader>
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="secondary">{post.category}</Badge>
                  <div className="flex items-center text-sm text-gray-500">
                    <Clock className="h-3 w-3 mr-1" />
                    {post.readTime} min read
                  </div>
                </div>
                <CardTitle className="text-lg hover:text-purple-600 dark:hover:text-lime-400 transition-colors">
                  <Link to={`/blog/${post.id}`}>
                    {post.title}
                  </Link>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                  {post.excerpt}
                </p>
                
                <div className="flex items-center justify-between text-sm text-gray-500">
                  <div className="flex items-center">
                    <User className="h-3 w-3 mr-1" />
                    {post.author}
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-3 w-3 mr-1" />
                    {formatDate(post.publishedAt)}
                  </div>
                </div>

                <div className="flex flex-wrap gap-1 mt-3">
                  {post.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      #{tag}
                    </Badge>
                  ))}
                </div>

                <Link to={`/blog/${post.id}`}>
                  <Button className="w-full mt-4" variant="outline">
                    Read More
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* No Results */}
        {filteredPosts.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500 dark:text-gray-400 text-lg">
              No articles found matching your criteria.
            </p>
          </div>
        )}
      </div>

      <Footer />
    </div>
  );
};

export default Blog;
