import { Navigate, useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';
import UserProfile from '@/components/UserProfile';
import RecentSearches from '@/components/RecentSearches';
import FavoriteCarModels from '@/components/FavoriteCarModels';
import Footer from '@/components/Footer';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Home } from 'lucide-react';
import { Link } from 'react-router-dom';

const Dashboard = () => {
  const { user, isLoading } = useAuth();
  const navigate = useNavigate();

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">Loading...</div>
      </div>
    );
  }
  
  // Redirect to auth page if not logged in
  if (!user) {
    return <Navigate to="/auth" replace />;
  }

  const handleCarModelSelect = (carModel: string) => {
    // Navigate to home page with the selected car model
    navigate('/', { state: { selectedCarModel: carModel } });
  };

  const handleSearchSelect = (query: string, carModel?: string) => {
    // Navigate to home page with the selected search query and car model
    navigate('/', { 
      state: { 
        selectedQuery: query,
        selectedCarModel: carModel 
      } 
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 flex flex-col">
      <div className="container mx-auto px-4 py-8 flex-grow">
        <div className="mb-6">
          <Link to="/">
            <Button variant="ghost" className="pl-0">
              <ArrowLeft className="mr-2 h-4 w-4" /> Back to Home
            </Button>
          </Link>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4">Menu</h2>
              <Link to="/" className="flex items-center text-gray-700 dark:text-gray-300 hover:text-rosak dark:hover:text-rosak transition-colors">
                <Home className="mr-2 h-4 w-4" /> Home
              </Link>
            </div>
            <UserProfile />
          </div>
          
          <div className="md:col-span-2 space-y-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Recent Searches</h2>
              <RecentSearches onSelectSearch={handleSearchSelect} />
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-xl font-semibold mb-4">Favorite Car Models</h2>
              <FavoriteCarModels onSelectCarModel={handleCarModelSelect} />
            </div>
          </div>
        </div>
      </div>
      
      <Footer />
    </div>
  );
};

export default Dashboard;
