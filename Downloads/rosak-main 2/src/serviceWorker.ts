
// This code registers a service worker for offline capabilities

// Define resources to cache
const CACHE_NAME = 'rosak-diagnostics-cache-v1';
const urlsToCache = [
  '/',
  '/index.html',
  '/main.js',
  '/main.css',
];

// Install service worker and cache static assets
self.addEventListener('install', (event: any) => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Opened cache');
        return cache.addAll(urlsToCache);
      })
  );
});

// Intercept network requests and serve cached content when offline
self.addEventListener('fetch', (event: any) => {
  event.respondWith(
    // Try the network first
    fetch(event.request)
      .then(response => {
        // Clone the response to cache it and return the original
        const responseToCache = response.clone();
        
        caches.open(CACHE_NAME)
          .then(cache => {
            // Only cache successful responses
            if (event.request.method === 'GET' && response.status === 200) {
              cache.put(event.request, responseToCache);
            }
          });
        
        return response;
      })
      .catch(() => {
        // If network fails, serve from cache
        return caches.match(event.request)
          .then(cachedResponse => {
            if (cachedResponse) {
              return cachedResponse;
            }
            
            // Show custom offline UI for HTML requests
            if (event.request.headers.get('accept')?.includes('text/html')) {
              return caches.match('/offline.html')
                .then(offlineResponse => {
                  return offlineResponse || new Response(
                    '<html><body><h1>You are offline</h1><p>Please check your internet connection.</p></body></html>',
                    {
                      headers: { 'Content-Type': 'text/html' }
                    }
                  );
                });
            }
            
            return new Response('Network error occurred', { status: 503 });
          });
      })
  );
});

// Clean up old caches when service worker is updated
self.addEventListener('activate', (event: any) => {
  const cacheWhitelist = [CACHE_NAME];
  
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          if (cacheWhitelist.indexOf(cacheName) === -1) {
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
});

export {};
