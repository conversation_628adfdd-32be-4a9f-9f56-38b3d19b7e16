# 🚗 RosakMY - AI-Powered Vehicle Diagnostics

RosakMY is a minimalist, AI-powered vehicle diagnostic platform designed to help users quickly identify and resolve car problems. Built with modern web technologies, it provides an intuitive interface for vehicle diagnostics and maintenance guidance.

                                                                        
## 🧠 Inspiration

Most drivers struggle to understand what's wrong with their vehicles — especially when vague symptoms arise. Workshop visits can be expensive and time-consuming, and Googling symptoms often leads to confusing results.

ROSAK.MY was built to make car diagnostics as simple as chatting with a friend. No jargon. No complicated tools. Just type what you’re experiencing, and get fast, clear suggestions powered by AI.

                    
## 💡 What It Does

- 🧾 AI-Powered Symptom Analysis                    
  Users type in a car issue (e.g., “my car makes a clicking noise when I turn”), and ROSAK.MY suggests the most likely problems.
    
- 🛠️ Troubleshooting Guidance                    
The platform offers a list of possible causes and next steps — whether it's safe to drive, whether to seek a mechanic, or if it's something you can fix yourself.
    
- 📱 Minimalist, Mobile-First UI                    
Built on the go — quick to load, easy to use.
    
- 🧠 Built-in Prompt Engineering                    
Custom-designed AI prompts provide relevant and structured outputs for vehicle issues.

                    
##  ✅ Features

- 🤖 AI-powered vehicle diagnostics
- 🔍 Real-time problem identification
- 🛠️ Maintenance recommendations
- 📱 User-friendly interface
- 🧑‍💻 Responsive design for all devices

                    
## 🔧 How We Built It

Tool/Stack	| Role
------------- | -------------
Lovable.dev / Bolt.new	| To brainstorm, wireframe, and scaffold the UI.
Cursor / Augment	| For hands-on code editing, API integration, and logic refinement.
Perplexity AI (Sonar)	| Natural language understanding and diagnostics generation.
Tailwind CSS	| Minimalist, mobile-first styling.
Vercel	| Seamless deployment and hosting.

> We used AI tools during development to ideate and speed up implementation — but core logic, diagnostic design, and UX were custom-built.

                    
## 🚧 Challenges We Faced

- 🧩 Translating vague human symptoms into meaningful diagnostics.
- ⚠️ Balancing AI creativity with safety and accuracy.
- 📱 Ensuring the experience feels fast and useful even on low-end mobile devices.
- 🇲🇾 Making the platform culturally and linguistically accessible to Malaysians.


                    
## 🔄 What’s Next

- 📸 Upload photo/video support for richer diagnostics.
- 📍 Location-based mechanic recommendations.
- 🔁 Save/share diagnostic reports.
- 🇲🇾 Full Bahasa Melayu experience.
- 🧠 AI model fine-tuning with actual user input.


                    
## 📚 Tech Stack

 Category    | Stack Used                             
 ----------- | -------------------------------------- 
 Frontend    | Tailwind CSS, JavaScript/React, TypeScript, Vite   
 Backend/API | Perplexity API, Supabase               
 AI Tools    | Bolt.new, Lovable.dev, Cursor, Augment 
 Deployment  | Vercel                                 


 ## 📸 Demo

- 📷 Screenshots:
  
![](https://github.com/faideology/rosak/blob/main/public/og-image.png)

- 🌐 Live Site: https://www.rosak.my

- 📽️ Demo Video: https://www.youtube.com/watch?v=oxBJLIEgv-k


